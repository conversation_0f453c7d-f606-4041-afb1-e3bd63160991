import React, { useState, useEffect } from 'react';
import './App.css';

const API_BASE_URL = 'https://5002-iatgt9eldn0mhx1sengox-b8c15b5d.manusvm.computer/api';

function App() {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [companies, setCompanies] = useState([]);
  const [customers, setCustomers] = useState([]);
  const [products, setProducts] = useState([]);
  const [invoices, setInvoices] = useState([]);
  const [analytics, setAnalytics] = useState(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      const [companiesRes, customersRes, productsRes, invoicesRes, analyticsRes] = await Promise.all([
        fetch(`${API_BASE_URL}/companies`),
        fetch(`${API_BASE_URL}/customers`),
        fetch(`${API_BASE_URL}/products`),
        fetch(`${API_BASE_URL}/invoices`),
        fetch(`${API_BASE_URL}/analytics/sales`)
      ]);

      setCompanies(await companiesRes.json());
      setCustomers(await customersRes.json());
      setProducts(await productsRes.json());
      setInvoices(await invoicesRes.json());
      setAnalytics(await analyticsRes.json());
    } catch (error) {
      console.error('Error loading data:', error);
    }
  };

  const createInvoice = async () => {
    const invoiceData = {
      customer_id: 1,
      invoice_number: `INV-${Date.now()}`,
      invoice_date: new Date().toISOString().split('T')[0],
      due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      items: [
        {
          product_id: 1,
          quantity: 1,
          unit_price: 1000.00,
          tax_rate: 15.0
        }
      ]
    };

    try {
      const response = await fetch(`${API_BASE_URL}/invoices`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(invoiceData)
      });
      
      if (response.ok) {
        const result = await response.json();
        alert(`تم إنشاء الفاتورة بنجاح! رقم الفاتورة: ${result.id}`);
        loadData();
      }
    } catch (error) {
      console.error('Error creating invoice:', error);
    }
  };

  const Dashboard = () => (
    <div className="dashboard">
      <h2>لوحة التحكم الرئيسية</h2>
      <div className="stats-grid">
        <div className="stat-card">
          <h3>إجمالي المبيعات</h3>
          <p className="stat-value">{analytics?.summary?.total_sales?.toFixed(2) || '0.00'} ريال</p>
        </div>
        <div className="stat-card">
          <h3>إجمالي الضرائب</h3>
          <p className="stat-value">{analytics?.summary?.total_tax?.toFixed(2) || '0.00'} ريال</p>
        </div>
        <div className="stat-card">
          <h3>عدد الفواتير</h3>
          <p className="stat-value">{analytics?.summary?.invoice_count || 0}</p>
        </div>
        <div className="stat-card">
          <h3>متوسط الفاتورة</h3>
          <p className="stat-value">{analytics?.summary?.average_invoice?.toFixed(2) || '0.00'} ريال</p>
        </div>
      </div>
      
      <div className="quick-actions">
        <h3>الإجراءات السريعة</h3>
        <button onClick={createInvoice} className="btn btn-primary">
          إنشاء فاتورة جديدة
        </button>
      </div>
    </div>
  );

  const Companies = () => (
    <div className="companies">
      <h2>إدارة الشركات</h2>
      <div className="data-table">
        <table>
          <thead>
            <tr>
              <th>الاسم</th>
              <th>الرقم الضريبي</th>
              <th>العنوان</th>
              <th>الهاتف</th>
              <th>البريد الإلكتروني</th>
            </tr>
          </thead>
          <tbody>
            {companies.map(company => (
              <tr key={company.id}>
                <td>{company.name}</td>
                <td>{company.tax_id}</td>
                <td>{company.address}</td>
                <td>{company.phone}</td>
                <td>{company.email}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  const Customers = () => (
    <div className="customers">
      <h2>إدارة العملاء</h2>
      <div className="data-table">
        <table>
          <thead>
            <tr>
              <th>الاسم</th>
              <th>الرقم الضريبي</th>
              <th>العنوان</th>
              <th>الهاتف</th>
              <th>البريد الإلكتروني</th>
            </tr>
          </thead>
          <tbody>
            {customers.map(customer => (
              <tr key={customer.id}>
                <td>{customer.name}</td>
                <td>{customer.tax_id}</td>
                <td>{customer.address}</td>
                <td>{customer.phone}</td>
                <td>{customer.email}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  const Products = () => (
    <div className="products">
      <h2>إدارة المنتجات</h2>
      <div className="data-table">
        <table>
          <thead>
            <tr>
              <th>الاسم</th>
              <th>الوصف</th>
              <th>السعر</th>
              <th>معدل الضريبة</th>
              <th>الفئة</th>
            </tr>
          </thead>
          <tbody>
            {products.map(product => (
              <tr key={product.id}>
                <td>{product.name}</td>
                <td>{product.description}</td>
                <td>{product.price} ريال</td>
                <td>{product.tax_rate}%</td>
                <td>{product.category}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  const Invoices = () => (
    <div className="invoices">
      <h2>إدارة الفواتير</h2>
      <div className="data-table">
        <table>
          <thead>
            <tr>
              <th>رقم الفاتورة</th>
              <th>العميل</th>
              <th>التاريخ</th>
              <th>المبلغ الإجمالي</th>
              <th>الحالة</th>
            </tr>
          </thead>
          <tbody>
            {invoices.map(invoice => (
              <tr key={invoice.id}>
                <td>{invoice.invoice_number}</td>
                <td>{invoice.customer_name}</td>
                <td>{invoice.invoice_date}</td>
                <td>{invoice.total_amount} ريال</td>
                <td>{invoice.status}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard': return <Dashboard />;
      case 'companies': return <Companies />;
      case 'customers': return <Customers />;
      case 'products': return <Products />;
      case 'invoices': return <Invoices />;
      default: return <Dashboard />;
    }
  };

  return (
    <div className="app" dir="rtl">
      <header className="app-header">
        <h1>نظام المحاسبة الإلكتروني المتوافق مع ZATCA</h1>
        <p>برنامج محاسبة احترافي للشركات السعودية</p>
      </header>

      <nav className="app-nav">
        <button 
          className={activeTab === 'dashboard' ? 'active' : ''} 
          onClick={() => setActiveTab('dashboard')}
        >
          لوحة التحكم
        </button>
        <button 
          className={activeTab === 'companies' ? 'active' : ''} 
          onClick={() => setActiveTab('companies')}
        >
          الشركات
        </button>
        <button 
          className={activeTab === 'customers' ? 'active' : ''} 
          onClick={() => setActiveTab('customers')}
        >
          العملاء
        </button>
        <button 
          className={activeTab === 'products' ? 'active' : ''} 
          onClick={() => setActiveTab('products')}
        >
          المنتجات
        </button>
        <button 
          className={activeTab === 'invoices' ? 'active' : ''} 
          onClick={() => setActiveTab('invoices')}
        >
          الفواتير
        </button>
      </nav>

      <main className="app-main">
        {renderContent()}
      </main>

      <footer className="app-footer">
        <p>© 2025 نظام المحاسبة الإلكتروني - متوافق مع متطلبات هيئة الزكاة والدخل السعودية</p>
      </footer>
    </div>
  );
}

export default App;

