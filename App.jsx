import React, { useState, useEffect } from 'react';
import './App.css';

const API_BASE_URL = 'http://localhost:5000/api';

function App() {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [companies, setCompanies] = useState([]);
  const [customers, setCustomers] = useState([]);
  const [products, setProducts] = useState([]);
  const [invoices, setInvoices] = useState([]);
  const [analytics, setAnalytics] = useState(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      const [companiesRes, customersRes, productsRes, invoicesRes, analyticsRes] = await Promise.all([
        fetch(`${API_BASE_URL}/companies`),
        fetch(`${API_BASE_URL}/customers`),
        fetch(`${API_BASE_URL}/products`),
        fetch(`${API_BASE_URL}/invoices`),
        fetch(`${API_BASE_URL}/analytics/sales`)
      ]);

      setCompanies(await companiesRes.json());
      setCustomers(await customersRes.json());
      setProducts(await productsRes.json());
      setInvoices(await invoicesRes.json());
      setAnalytics(await analyticsRes.json());
    } catch (error) {
      console.error('Error loading data:', error);
    }
  };

  const createInvoice = async () => {
    const invoiceData = {
      customer_id: 1,
      invoice_number: `INV-${Date.now()}`,
      invoice_date: new Date().toISOString().split('T')[0],
      due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      items: [
        {
          product_id: 1,
          quantity: 1,
          unit_price: 1000.00,
          tax_rate: 15.0
        }
      ]
    };

    try {
      const response = await fetch(`${API_BASE_URL}/invoices`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(invoiceData)
      });

      if (response.ok) {
        const result = await response.json();
        alert(`تم إنشاء الفاتورة بنجاح! رقم الفاتورة: ${result.id}`);
        loadData();
      }
    } catch (error) {
      console.error('Error creating invoice:', error);
    }
  };

  const Dashboard = () => (
    <div className="dashboard">
      <h2>لوحة التحكم الرئيسية</h2>
      <div className="stats-grid">
        <div className="stat-card">
          <h3>إجمالي المبيعات</h3>
          <p className="stat-value">{analytics?.summary?.total_sales?.toFixed(2) || '0.00'} ريال</p>
        </div>
        <div className="stat-card">
          <h3>إجمالي الضرائب</h3>
          <p className="stat-value">{analytics?.summary?.total_tax?.toFixed(2) || '0.00'} ريال</p>
        </div>
        <div className="stat-card">
          <h3>عدد الفواتير</h3>
          <p className="stat-value">{analytics?.summary?.invoice_count || 0}</p>
        </div>
        <div className="stat-card">
          <h3>متوسط الفاتورة</h3>
          <p className="stat-value">{analytics?.summary?.average_invoice?.toFixed(2) || '0.00'} ريال</p>
        </div>
      </div>

      <div className="quick-actions">
        <h3>الإجراءات السريعة</h3>
        <button onClick={createInvoice} className="btn btn-primary">
          إنشاء فاتورة جديدة
        </button>
      </div>
    </div>
  );

  const Companies = () => {
    const [showForm, setShowForm] = useState(false);
    const [formData, setFormData] = useState({
      name: '',
      tax_id: '',
      address: '',
      phone: '',
      email: ''
    });

    const handleSubmit = async (e) => {
      e.preventDefault();
      try {
        const response = await fetch(`${API_BASE_URL}/companies`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(formData)
        });

        if (response.ok) {
          alert('تم إنشاء الشركة بنجاح');
          setFormData({ name: '', tax_id: '', address: '', phone: '', email: '' });
          setShowForm(false);
          loadData();
        } else {
          alert('حدث خطأ في إنشاء الشركة');
        }
      } catch (error) {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
      }
    };

    return (
      <div className="companies">
        <div className="section-header">
          <h2>إدارة الشركات</h2>
          <button
            onClick={() => setShowForm(!showForm)}
            className="btn btn-primary"
          >
            {showForm ? 'إلغاء' : 'إضافة شركة جديدة'}
          </button>
        </div>

        {showForm && (
          <div className="form-container">
            <h3>إضافة شركة جديدة</h3>
            <form onSubmit={handleSubmit}>
              <div className="form-group">
                <label>اسم الشركة *</label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  required
                />
              </div>
              <div className="form-group">
                <label>الرقم الضريبي</label>
                <input
                  type="text"
                  value={formData.tax_id}
                  onChange={(e) => setFormData({...formData, tax_id: e.target.value})}
                />
              </div>
              <div className="form-group">
                <label>العنوان</label>
                <textarea
                  value={formData.address}
                  onChange={(e) => setFormData({...formData, address: e.target.value})}
                ></textarea>
              </div>
              <div className="form-group">
                <label>رقم الهاتف</label>
                <input
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => setFormData({...formData, phone: e.target.value})}
                />
              </div>
              <div className="form-group">
                <label>البريد الإلكتروني</label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData({...formData, email: e.target.value})}
                />
              </div>
              <div className="form-actions">
                <button type="submit" className="btn btn-primary">حفظ</button>
                <button type="button" onClick={() => setShowForm(false)} className="btn btn-secondary">إلغاء</button>
              </div>
            </form>
          </div>
        )}

        <div className="data-table">
          <table>
            <thead>
              <tr>
                <th>الاسم</th>
                <th>الرقم الضريبي</th>
                <th>العنوان</th>
                <th>الهاتف</th>
                <th>البريد الإلكتروني</th>
              </tr>
            </thead>
            <tbody>
              {companies.map(company => (
                <tr key={company.id}>
                  <td>{company.name}</td>
                  <td>{company.tax_id}</td>
                  <td>{company.address}</td>
                  <td>{company.phone}</td>
                  <td>{company.email}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  const Customers = () => {
    const [showForm, setShowForm] = useState(false);
    const [formData, setFormData] = useState({
      name: '',
      tax_id: '',
      address: '',
      phone: '',
      email: ''
    });

    const handleSubmit = async (e) => {
      e.preventDefault();
      try {
        const response = await fetch(`${API_BASE_URL}/customers`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({...formData, company_id: 1})
        });

        if (response.ok) {
          alert('تم إنشاء العميل بنجاح');
          setFormData({ name: '', tax_id: '', address: '', phone: '', email: '' });
          setShowForm(false);
          loadData();
        } else {
          alert('حدث خطأ في إنشاء العميل');
        }
      } catch (error) {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
      }
    };

    return (
      <div className="customers">
        <div className="section-header">
          <h2>إدارة العملاء</h2>
          <button
            onClick={() => setShowForm(!showForm)}
            className="btn btn-primary"
          >
            {showForm ? 'إلغاء' : 'إضافة عميل جديد'}
          </button>
        </div>

        {showForm && (
          <div className="form-container">
            <h3>إضافة عميل جديد</h3>
            <form onSubmit={handleSubmit}>
              <div className="form-group">
                <label>اسم العميل *</label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  required
                />
              </div>
              <div className="form-group">
                <label>الرقم الضريبي</label>
                <input
                  type="text"
                  value={formData.tax_id}
                  onChange={(e) => setFormData({...formData, tax_id: e.target.value})}
                />
              </div>
              <div className="form-group">
                <label>العنوان</label>
                <textarea
                  value={formData.address}
                  onChange={(e) => setFormData({...formData, address: e.target.value})}
                ></textarea>
              </div>
              <div className="form-group">
                <label>رقم الهاتف</label>
                <input
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => setFormData({...formData, phone: e.target.value})}
                />
              </div>
              <div className="form-group">
                <label>البريد الإلكتروني</label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData({...formData, email: e.target.value})}
                />
              </div>
              <div className="form-actions">
                <button type="submit" className="btn btn-primary">حفظ</button>
                <button type="button" onClick={() => setShowForm(false)} className="btn btn-secondary">إلغاء</button>
              </div>
            </form>
          </div>
        )}

        <div className="data-table">
          <table>
            <thead>
              <tr>
                <th>الاسم</th>
                <th>الرقم الضريبي</th>
                <th>العنوان</th>
                <th>الهاتف</th>
                <th>البريد الإلكتروني</th>
              </tr>
            </thead>
            <tbody>
              {customers.map(customer => (
                <tr key={customer.id}>
                  <td>{customer.name}</td>
                  <td>{customer.tax_id}</td>
                  <td>{customer.address}</td>
                  <td>{customer.phone}</td>
                  <td>{customer.email}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  const Products = () => {
    const [showForm, setShowForm] = useState(false);
    const [formData, setFormData] = useState({
      name: '',
      description: '',
      price: '',
      tax_rate: '15.0',
      category: ''
    });

    const handleSubmit = async (e) => {
      e.preventDefault();
      try {
        const response = await fetch(`${API_BASE_URL}/products`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({...formData, company_id: 1, price: parseFloat(formData.price), tax_rate: parseFloat(formData.tax_rate)})
        });

        if (response.ok) {
          alert('تم إنشاء المنتج بنجاح');
          setFormData({ name: '', description: '', price: '', tax_rate: '15.0', category: '' });
          setShowForm(false);
          loadData();
        } else {
          alert('حدث خطأ في إنشاء المنتج');
        }
      } catch (error) {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
      }
    };

    return (
      <div className="products">
        <div className="section-header">
          <h2>إدارة المنتجات</h2>
          <button
            onClick={() => setShowForm(!showForm)}
            className="btn btn-primary"
          >
            {showForm ? 'إلغاء' : 'إضافة منتج جديد'}
          </button>
        </div>

        {showForm && (
          <div className="form-container">
            <h3>إضافة منتج جديد</h3>
            <form onSubmit={handleSubmit}>
              <div className="form-group">
                <label>اسم المنتج *</label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  required
                />
              </div>
              <div className="form-group">
                <label>الوصف</label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({...formData, description: e.target.value})}
                ></textarea>
              </div>
              <div className="form-group">
                <label>السعر (ريال) *</label>
                <input
                  type="number"
                  step="0.01"
                  value={formData.price}
                  onChange={(e) => setFormData({...formData, price: e.target.value})}
                  required
                />
              </div>
              <div className="form-group">
                <label>معدل الضريبة (%)</label>
                <input
                  type="number"
                  step="0.01"
                  value={formData.tax_rate}
                  onChange={(e) => setFormData({...formData, tax_rate: e.target.value})}
                />
              </div>
              <div className="form-group">
                <label>الفئة</label>
                <input
                  type="text"
                  value={formData.category}
                  onChange={(e) => setFormData({...formData, category: e.target.value})}
                />
              </div>
              <div className="form-actions">
                <button type="submit" className="btn btn-primary">حفظ</button>
                <button type="button" onClick={() => setShowForm(false)} className="btn btn-secondary">إلغاء</button>
              </div>
            </form>
          </div>
        )}

        <div className="data-table">
          <table>
            <thead>
              <tr>
                <th>الاسم</th>
                <th>الوصف</th>
                <th>السعر</th>
                <th>معدل الضريبة</th>
                <th>الفئة</th>
              </tr>
            </thead>
            <tbody>
              {products.map(product => (
                <tr key={product.id}>
                  <td>{product.name}</td>
                  <td>{product.description}</td>
                  <td>{product.price} ريال</td>
                  <td>{product.tax_rate}%</td>
                  <td>{product.category}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  const Invoices = () => {
    const [showForm, setShowForm] = useState(false);
    const [formData, setFormData] = useState({
      customer_id: '',
      invoice_number: '',
      invoice_date: new Date().toISOString().split('T')[0],
      due_date: '',
      items: [{ product_id: '', quantity: 1, unit_price: 0, tax_rate: 15.0 }]
    });

    const addItem = () => {
      setFormData({
        ...formData,
        items: [...formData.items, { product_id: '', quantity: 1, unit_price: 0, tax_rate: 15.0 }]
      });
    };

    const removeItem = (index) => {
      const newItems = formData.items.filter((_, i) => i !== index);
      setFormData({ ...formData, items: newItems });
    };

    const updateItem = (index, field, value) => {
      const newItems = [...formData.items];
      newItems[index] = { ...newItems[index], [field]: value };
      setFormData({ ...formData, items: newItems });
    };

    const handleSubmit = async (e) => {
      e.preventDefault();
      try {
        const response = await fetch(`${API_BASE_URL}/invoices`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({...formData, company_id: 1})
        });

        if (response.ok) {
          const result = await response.json();
          alert('تم إنشاء الفاتورة بنجاح');
          setFormData({
            customer_id: '',
            invoice_number: '',
            invoice_date: new Date().toISOString().split('T')[0],
            due_date: '',
            items: [{ product_id: '', quantity: 1, unit_price: 0, tax_rate: 15.0 }]
          });
          setShowForm(false);
          loadData();
        } else {
          alert('حدث خطأ في إنشاء الفاتورة');
        }
      } catch (error) {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
      }
    };

    return (
      <div className="invoices">
        <div className="section-header">
          <h2>إدارة الفواتير</h2>
          <button
            onClick={() => setShowForm(!showForm)}
            className="btn btn-primary"
          >
            {showForm ? 'إلغاء' : 'إنشاء فاتورة جديدة'}
          </button>
        </div>

        {showForm && (
          <div className="form-container">
            <h3>إنشاء فاتورة جديدة</h3>
            <form onSubmit={handleSubmit}>
              <div className="form-row">
                <div className="form-group">
                  <label>رقم الفاتورة *</label>
                  <input
                    type="text"
                    value={formData.invoice_number}
                    onChange={(e) => setFormData({...formData, invoice_number: e.target.value})}
                    required
                  />
                </div>
                <div className="form-group">
                  <label>العميل *</label>
                  <select
                    value={formData.customer_id}
                    onChange={(e) => setFormData({...formData, customer_id: e.target.value})}
                    required
                  >
                    <option value="">اختر عميل</option>
                    {customers.map(customer => (
                      <option key={customer.id} value={customer.id}>{customer.name}</option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label>تاريخ الفاتورة *</label>
                  <input
                    type="date"
                    value={formData.invoice_date}
                    onChange={(e) => setFormData({...formData, invoice_date: e.target.value})}
                    required
                  />
                </div>
                <div className="form-group">
                  <label>تاريخ الاستحقاق</label>
                  <input
                    type="date"
                    value={formData.due_date}
                    onChange={(e) => setFormData({...formData, due_date: e.target.value})}
                  />
                </div>
              </div>

              <div className="invoice-items">
                <h4>عناصر الفاتورة</h4>
                {formData.items.map((item, index) => (
                  <div key={index} className="item-row">
                    <select
                      value={item.product_id}
                      onChange={(e) => {
                        const product = products.find(p => p.id == e.target.value);
                        updateItem(index, 'product_id', e.target.value);
                        if (product) {
                          updateItem(index, 'unit_price', product.price);
                          updateItem(index, 'tax_rate', product.tax_rate);
                        }
                      }}
                      required
                    >
                      <option value="">اختر منتج</option>
                      {products.map(product => (
                        <option key={product.id} value={product.id}>{product.name}</option>
                      ))}
                    </select>

                    <input
                      type="number"
                      placeholder="الكمية"
                      value={item.quantity}
                      onChange={(e) => updateItem(index, 'quantity', parseFloat(e.target.value) || 0)}
                      min="1"
                      required
                    />

                    <input
                      type="number"
                      placeholder="سعر الوحدة"
                      value={item.unit_price}
                      onChange={(e) => updateItem(index, 'unit_price', parseFloat(e.target.value) || 0)}
                      step="0.01"
                      required
                    />

                    <input
                      type="number"
                      placeholder="معدل الضريبة"
                      value={item.tax_rate}
                      onChange={(e) => updateItem(index, 'tax_rate', parseFloat(e.target.value) || 0)}
                      step="0.01"
                    />

                    <button type="button" onClick={() => removeItem(index)} className="btn btn-danger">حذف</button>
                  </div>
                ))}
                <button type="button" onClick={addItem} className="btn btn-secondary">إضافة عنصر</button>
              </div>

              <div className="form-actions">
                <button type="submit" className="btn btn-primary">إنشاء الفاتورة</button>
                <button type="button" onClick={() => setShowForm(false)} className="btn btn-secondary">إلغاء</button>
              </div>
            </form>
          </div>
        )}

        <div className="data-table">
          <table>
            <thead>
              <tr>
                <th>رقم الفاتورة</th>
                <th>العميل</th>
                <th>التاريخ</th>
                <th>المبلغ الإجمالي</th>
                <th>الحالة</th>
              </tr>
            </thead>
            <tbody>
              {invoices.map(invoice => (
                <tr key={invoice.id}>
                  <td>{invoice.invoice_number}</td>
                  <td>{invoice.customer_name}</td>
                  <td>{invoice.invoice_date}</td>
                  <td>{invoice.total_amount} ريال</td>
                  <td>{invoice.status}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard': return <Dashboard />;
      case 'companies': return <Companies />;
      case 'customers': return <Customers />;
      case 'products': return <Products />;
      case 'invoices': return <Invoices />;
      default: return <Dashboard />;
    }
  };

  return (
    <div className="app" dir="rtl">
      <header className="app-header">
        <h1>نظام المحاسبة الإلكتروني المتوافق مع ZATCA</h1>
        <p>برنامج محاسبة احترافي للشركات السعودية</p>
      </header>

      <nav className="app-nav">
        <button
          className={activeTab === 'dashboard' ? 'active' : ''}
          onClick={() => setActiveTab('dashboard')}
        >
          لوحة التحكم
        </button>
        <button
          className={activeTab === 'companies' ? 'active' : ''}
          onClick={() => setActiveTab('companies')}
        >
          الشركات
        </button>
        <button
          className={activeTab === 'customers' ? 'active' : ''}
          onClick={() => setActiveTab('customers')}
        >
          العملاء
        </button>
        <button
          className={activeTab === 'products' ? 'active' : ''}
          onClick={() => setActiveTab('products')}
        >
          المنتجات
        </button>
        <button
          className={activeTab === 'invoices' ? 'active' : ''}
          onClick={() => setActiveTab('invoices')}
        >
          الفواتير
        </button>
      </nav>

      <main className="app-main">
        {renderContent()}
      </main>

      <footer className="app-footer">
        <p>© 2025 نظام المحاسبة الإلكتروني - متوافق مع متطلبات هيئة الزكاة والدخل السعودية</p>
      </footer>
    </div>
  );
}

export default App;

