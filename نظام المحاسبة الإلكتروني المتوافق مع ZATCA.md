# نظام المحاسبة الإلكتروني المتوافق مع ZATCA

## نظرة عامة

تم تطوير نظام محاسبة إلكتروني احترافي ومتكامل للشركات السعودية، متوافق بالكامل مع متطلبات المرحلة الثانية من هيئة الزكاة والدخل السعودية (ZATCA). النظام يوفر حلولاً شاملة لإدارة العمليات المحاسبية وإنشاء الفواتير الإلكترونية.

## 🚀 الميزات الرئيسية

### ✅ إدارة البيانات الأساسية
- **إدارة الشركات**: تسجيل وإدارة بيانات الشركات مع الأرقام الضريبية
- **إدارة العملاء**: قاعدة بيانات شاملة للعملاء مع معلومات الاتصال
- **إدارة المنتجات**: كتالوج متكامل للمنتجات والخدمات مع الأسعار

### 🧾 نظام الفواتير المتقدم
- **فواتير متوافقة مع ZATCA**: إنشاء فواتير تلبي جميع متطلبات المرحلة الثانية
- **QR Code متقدم**: رموز QR مشفرة بتقنية TLV وفقاً لمعايير ZATCA
- **حساب تلقائي للضرائب**: حساب دقيق لضريبة القيمة المضافة بمعدل 15%
- **ترقيم تلقائي**: نظام ترقيم تلقائي للفواتير

### 📊 التحليلات والتقارير
- **تحليل المبيعات**: تقارير مفصلة عن الأداء المالي
- **تقارير ضريبة القيمة المضافة**: تقارير متخصصة للامتثال الضريبي
- **أفضل المنتجات مبيعاً**: تحليل أداء المنتجات
- **المبيعات اليومية**: تتبع الأداء اليومي

## 🏗️ البنية التقنية

### Backend (خادم التطبيق)
- **التقنية**: Flask (Python)
- **قاعدة البيانات**: SQLite مع إمكانية التطوير لـ PostgreSQL
- **المصادقة**: JWT Token-based Authentication
- **API**: RESTful API مع CORS Support

### Frontend (واجهة الويب)
- **التقنية**: React.js مع Vite
- **التصميم**: CSS3 مع دعم RTL للغة العربية
- **التفاعل**: Responsive Design متوافق مع الأجهزة المحمولة

### Desktop Application (تطبيق سطح المكتب)
- **التقنية**: Electron
- **التكامل**: مدمج مع واجهة الويب
- **الأيقونة**: أيقونة مخصصة احترافية

## 📡 واجهات برمجة التطبيقات (API)

### الرابط الأساسي
```
https://5002-iatgt9eldn0mhx1sengox-b8c15b5d.manusvm.computer/api
```

### نقاط النهاية الرئيسية

#### إدارة الشركات
- `GET /api/companies` - جلب جميع الشركات
- `POST /api/companies` - إنشاء شركة جديدة
- `GET /api/companies/{id}` - جلب شركة محددة
- `PUT /api/companies/{id}` - تحديث شركة
- `DELETE /api/companies/{id}` - حذف شركة

#### إدارة العملاء
- `GET /api/customers` - جلب جميع العملاء
- `POST /api/customers` - إنشاء عميل جديد

#### إدارة المنتجات
- `GET /api/products` - جلب جميع المنتجات
- `POST /api/products` - إنشاء منتج جديد

#### إدارة الفواتير
- `GET /api/invoices` - جلب جميع الفواتير
- `POST /api/invoices` - إنشاء فاتورة جديدة
- `GET /api/invoices/{id}` - جلب فاتورة محددة

#### التحليلات والتقارير
- `GET /api/analytics/sales` - تحليل المبيعات
- `GET /api/reports/vat` - تقرير ضريبة القيمة المضافة

#### أدوات مساعدة
- `GET /api/health` - فحص حالة النظام
- `POST /api/seed-data` - إضافة بيانات تجريبية

## 🔧 متطلبات ZATCA المطبقة

### المرحلة الثانية - الفواتير الإلكترونية
- ✅ **QR Code متقدم**: تشفير TLV مع البيانات المطلوبة
- ✅ **الرقم الضريبي**: تضمين الرقم الضريبي للبائع والمشتري
- ✅ **التوقيت الزمني**: طابع زمني دقيق للفواتير
- ✅ **المبالغ المطلوبة**: إجمالي المبلغ وضريبة القيمة المضافة
- ✅ **التشفير**: تشفير البيانات بـ Base64

### عناصر QR Code
1. **اسم البائع** (Tag 1)
2. **الرقم الضريبي** (Tag 2)
3. **التاريخ والوقت** (Tag 3)
4. **إجمالي المبلغ** (Tag 4)
5. **ضريبة القيمة المضافة** (Tag 5)

## 🌐 الروابط المباشرة

### واجهة النظام
- **الواجهة الرئيسية**: https://8080-iatgt9eldn0mhx1sengox-b8c15b5d.manusvm.computer
- **API Backend**: https://5002-iatgt9eldn0mhx1sengox-b8c15b5d.manusvm.computer

### أمثلة للاختبار
- **عرض الشركات**: https://5002-iatgt9eldn0mhx1sengox-b8c15b5d.manusvm.computer/api/companies
- **تحليل المبيعات**: https://5002-iatgt9eldn0mhx1sengox-b8c15b5d.manusvm.computer/api/analytics/sales
- **فحص النظام**: https://5002-iatgt9eldn0mhx1sengox-b8c15b5d.manusvm.computer/api/health

## 📋 البيانات التجريبية

تم إضافة بيانات تجريبية تشمل:

### الشركة التجريبية
- **الاسم**: شركة التقنية المتقدمة
- **الرقم الضريبي**: 300123456789003
- **العنوان**: الرياض، المملكة العربية السعودية
- **الهاتف**: +966112345678
- **البريد الإلكتروني**: <EMAIL>

### العملاء التجريبيون
1. **عميل تجريبي 1** - الرقم الضريبي: 310123456789001
2. **عميل تجريبي 2** - الرقم الضريبي: 310123456789002

### المنتجات التجريبية
1. **خدمة استشارية** - 1,000 ريال (ضريبة 15%)
2. **برنامج محاسبة** - 5,000 ريال (ضريبة 15%)
3. **دعم فني** - 500 ريال (ضريبة 15%)

## 🧪 اختبار النظام

### إنشاء فاتورة تجريبية
```bash
curl -X POST https://5002-iatgt9eldn0mhx1sengox-b8c15b5d.manusvm.computer/api/invoices \
  -H "Content-Type: application/json" \
  -d '{
    "customer_id": 1,
    "invoice_number": "INV-TEST-001",
    "invoice_date": "2025-07-14",
    "due_date": "2025-08-14",
    "items": [
      {
        "product_id": 1,
        "quantity": 1,
        "unit_price": 1000.00,
        "tax_rate": 15.0
      }
    ]
  }'
```

### النتيجة المتوقعة
```json
{
  "id": 1,
  "message": "تم إنشاء الفاتورة بنجاح",
  "total_amount": 1150.0,
  "qr_code": "ASjYtNix2YPYqSDYp9mE2KrZgtmG2YrYqSDYp9mE2YXYqtmC2K/ZhdipAg8zMDAxMjM0NTY3ODkwMDMDCjIwMjUtMDctMTQEBjExNTAuMAUFMTUwLjA=",
  "qr_image": "iVBORw0KGgoAAAANSUhEUgAAAeoAAAHqAQAAAADjFjCX..."
}
```

## 🔒 الأمان والحماية

- **CORS**: تم تفعيل CORS للسماح بالوصول من مختلف المصادر
- **التشفير**: تشفير QR Code بمعايير ZATCA
- **التحقق**: التحقق من صحة البيانات قبل الحفظ
- **قاعدة البيانات**: استخدام SQLite مع إمكانية الترقية

## 📱 التوافق

- **المتصفحات**: جميع المتصفحات الحديثة
- **الأجهزة**: أجهزة سطح المكتب والأجهزة المحمولة
- **أنظمة التشغيل**: Windows, macOS, Linux
- **اللغة**: دعم كامل للغة العربية (RTL)

## 🚀 التطوير المستقبلي

### المرحلة التالية
- **تكامل مع منصة فاتورة**: ربط مباشر مع منصة ZATCA
- **تقارير متقدمة**: تقارير مالية أكثر تفصيلاً
- **نظام المخزون**: إدارة المخزون والمواد
- **تطبيق الجوال**: تطبيق iOS و Android

### التحسينات التقنية
- **قاعدة بيانات**: الترقية إلى PostgreSQL للإنتاج
- **الأمان**: إضافة طبقات أمان إضافية
- **الأداء**: تحسين الأداء والسرعة
- **النسخ الاحتياطي**: نظام نسخ احتياطي تلقائي

## 📞 الدعم والمساعدة

للحصول على الدعم أو المساعدة:
- **التوثيق**: هذا الملف يحتوي على جميع المعلومات المطلوبة
- **الاختبار**: استخدم الروابط المباشرة للاختبار
- **API**: جميع نقاط النهاية موثقة ومتاحة للاستخدام

---

**© 2025 نظام المحاسبة الإلكتروني - تم تطويره بأحدث التقنيات العالمية**

*متوافق بالكامل مع متطلبات هيئة الزكاة والدخل السعودية (ZATCA)*

