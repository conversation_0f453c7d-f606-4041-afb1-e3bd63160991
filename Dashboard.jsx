import React from 'react';
import { useAuth } from '../contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  UserIcon, 
  BuildingOfficeIcon, 
  DocumentTextIcon, 
  CurrencyDollarIcon,
  ChartBarIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline';

const Dashboard = () => {
  const { user, logout } = useAuth();

  const stats = [
    {
      title: 'إجمالي الفواتير',
      value: '156',
      icon: DocumentTextIcon,
      color: 'bg-blue-500'
    },
    {
      title: 'العملاء',
      value: '42',
      icon: UserIcon,
      color: 'bg-green-500'
    },
    {
      title: 'المبيعات الشهرية',
      value: '125,000 ر.س',
      icon: CurrencyDollarIcon,
      color: 'bg-yellow-500'
    },
    {
      title: 'الفواتير المعلقة',
      value: '8',
      icon: ChartBarIcon,
      color: 'bg-red-500'
    }
  ];

  const quickActions = [
    {
      title: 'إنشاء فاتورة جديدة',
      description: 'إنشاء فاتورة مبيعات جديدة',
      icon: DocumentTextIcon,
      action: () => console.log('Create invoice')
    },
    {
      title: 'إضافة عميل',
      description: 'إضافة عميل جديد للنظام',
      icon: UserIcon,
      action: () => console.log('Add customer')
    },
    {
      title: 'إدارة المنتجات',
      description: 'عرض وإدارة المنتجات والخدمات',
      icon: BuildingOfficeIcon,
      action: () => console.log('Manage products')
    },
    {
      title: 'الإعدادات',
      description: 'إعدادات النظام والشركة',
      icon: Cog6ToothIcon,
      action: () => console.log('Settings')
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-semibold text-gray-900">
                نظام المحاسبة الإلكتروني
              </h1>
            </div>
            <div className="flex items-center space-x-4 space-x-reverse">
              <span className="text-sm text-gray-700">
                مرحباً، {user?.username}
              </span>
              <Button variant="outline" onClick={logout}>
                تسجيل الخروج
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Welcome Section */}
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              لوحة التحكم
            </h2>
            <p className="text-gray-600">
              مرحباً بك في نظام المحاسبة الإلكتروني. يمكنك من هنا إدارة جميع عمليات الشركة.
            </p>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {stats.map((stat, index) => (
              <Card key={index}>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <div className={`p-3 rounded-lg ${stat.color} text-white ml-4`}>
                      <stat.icon className="h-6 w-6" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        {stat.title}
                      </p>
                      <p className="text-2xl font-bold text-gray-900">
                        {stat.value}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Quick Actions */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              الإجراءات السريعة
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {quickActions.map((action, index) => (
                <Card key={index} className="cursor-pointer hover:shadow-md transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-center">
                      <action.icon className="h-8 w-8 text-blue-600 ml-3" />
                      <div>
                        <CardTitle className="text-base">{action.title}</CardTitle>
                        <CardDescription className="text-sm">
                          {action.description}
                        </CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                </Card>
              ))}
            </div>
          </div>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle>النشاط الأخير</CardTitle>
              <CardDescription>
                آخر العمليات التي تمت في النظام
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between py-2 border-b">
                  <div className="flex items-center">
                    <DocumentTextIcon className="h-5 w-5 text-blue-600 ml-3" />
                    <div>
                      <p className="text-sm font-medium">تم إنشاء فاتورة جديدة #INV-001</p>
                      <p className="text-xs text-gray-500">منذ ساعتين</p>
                    </div>
                  </div>
                  <span className="text-sm text-green-600">1,250 ر.س</span>
                </div>
                <div className="flex items-center justify-between py-2 border-b">
                  <div className="flex items-center">
                    <UserIcon className="h-5 w-5 text-green-600 ml-3" />
                    <div>
                      <p className="text-sm font-medium">تم إضافة عميل جديد</p>
                      <p className="text-xs text-gray-500">منذ 4 ساعات</p>
                    </div>
                  </div>
                </div>
                <div className="flex items-center justify-between py-2">
                  <div className="flex items-center">
                    <CurrencyDollarIcon className="h-5 w-5 text-yellow-600 ml-3" />
                    <div>
                      <p className="text-sm font-medium">تم استلام دفعة من العميل</p>
                      <p className="text-xs text-gray-500">أمس</p>
                    </div>
                  </div>
                  <span className="text-sm text-green-600">2,500 ر.س</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
};

export default Dashboard;

