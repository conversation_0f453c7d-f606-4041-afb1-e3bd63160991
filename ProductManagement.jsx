import React, { useState, useEffect } from 'react';
import { productsAPI } from '../services/api';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter } from '@/components/ui/dialog';
import { PencilIcon, TrashIcon, PlusIcon } from '@heroicons/react/24/outline';
import { useAuth } from '../contexts/AuthContext';

const ProductManagement = () => {
  const { user } = useAuth();
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentProduct, setCurrentProduct] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    unit_price: '',
    tax_rate: 15.0, // Default VAT rate in Saudi Arabia
    is_service: false
  });

  useEffect(() => {
    if (user && user.company_id) {
      fetchProducts(user.company_id);
    }
  }, [user]);

  const fetchProducts = async (companyId) => {
    try {
      setLoading(true);
      const response = await productsAPI.getAll(companyId);
      setProducts(response.data);
    } catch (err) {
      setError('فشل في جلب بيانات المنتجات.');
      console.error('Error fetching products:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { id, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [id]: type === 'checkbox' ? checked : value
    }));
  };

  const handleAddEditProduct = async (e) => {
    e.preventDefault();
    setError('');
    try {
      const dataToSend = {
        ...formData,
        unit_price: parseFloat(formData.unit_price),
        tax_rate: parseFloat(formData.tax_rate)
      };

      if (currentProduct) {
        await productsAPI.update(currentProduct.product_id, dataToSend);
      } else {
        await productsAPI.create({ ...dataToSend, company_id: user.company_id });
      }
      fetchProducts(user.company_id);
      setIsModalOpen(false);
      setFormData({
        name: '',
        description: '',
        unit_price: '',
        tax_rate: 15.0,
        is_service: false
      });
      setCurrentProduct(null);
    } catch (err) {
      setError(err.response?.data?.error || 'فشل في حفظ بيانات المنتج.');
      console.error('Error saving product:', err);
    }
  };

  const handleDeleteProduct = async (productId) => {
    if (window.confirm('هل أنت متأكد أنك تريد حذف هذا المنتج؟')) {
      setError('');
      try {
        await productsAPI.delete(productId);
        fetchProducts(user.company_id);
      } catch (err) {
        setError(err.response?.data?.error || 'فشل في حذف المنتج.');
        console.error('Error deleting product:', err);
      }
    }
  };

  const openEditModal = (product) => {
    setCurrentProduct(product);
    setFormData({
      name: product.name,
      description: product.description,
      unit_price: product.unit_price,
      tax_rate: product.tax_rate,
      is_service: product.is_service
    });
    setIsModalOpen(true);
  };

  const openAddModal = () => {
    setCurrentProduct(null);
    setFormData({
      name: '',
      description: '',
      unit_price: '',
      tax_rate: 15.0,
      is_service: false
    });
    setIsModalOpen(true);
  };

  if (loading) {
    return <div className="text-center py-8">جاري التحميل...</div>;
  }

  if (error) {
    return <div className="text-center py-8 text-red-500">خطأ: {error}</div>;
  }

  return (
    <div className="container mx-auto p-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-2xl font-bold">إدارة المنتجات والخدمات</CardTitle>
          <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
            <DialogTrigger asChild>
              <Button onClick={openAddModal}>
                <PlusIcon className="h-5 w-5 ml-2" /> إضافة منتج/خدمة جديدة
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>{currentProduct ? 'تعديل منتج/خدمة' : 'إضافة منتج/خدمة جديدة'}</DialogTitle>
                <DialogDescription>
                  {currentProduct ? 'قم بتعديل بيانات المنتج أو الخدمة.' : 'قم بإضافة منتج أو خدمة جديدة إلى النظام.'}
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleAddEditProduct} className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="name" className="text-right">الاسم</Label>
                  <Input id="name" value={formData.name} onChange={handleInputChange} className="col-span-3" required />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="description" className="text-right">الوصف</Label>
                  <Input id="description" value={formData.description} onChange={handleInputChange} className="col-span-3" />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="unit_price" className="text-right">سعر الوحدة</Label>
                  <Input id="unit_price" type="number" step="0.01" value={formData.unit_price} onChange={handleInputChange} className="col-span-3" required />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="tax_rate" className="text-right">نسبة الضريبة (%)</Label>
                  <Input id="tax_rate" type="number" step="0.01" value={formData.tax_rate} onChange={handleInputChange} className="col-span-3" />
                </div>
                <div className="flex items-center space-x-2 space-x-reverse">
                  <input
                    type="checkbox"
                    id="is_service"
                    checked={formData.is_service}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <Label htmlFor="is_service">هل هي خدمة؟</Label>
                </div>
                <DialogFooter>
                  <Button type="submit">{currentProduct ? 'حفظ التعديلات' : 'إضافة منتج/خدمة'}</Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>الاسم</TableHead>
                <TableHead>الوصف</TableHead>
                <TableHead>سعر الوحدة</TableHead>
                <TableHead>نسبة الضريبة</TableHead>
                <TableHead>خدمة؟</TableHead>
                <TableHead className="text-right">الإجراءات</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {products.map((product) => (
                <TableRow key={product.product_id}>
                  <TableCell className="font-medium">{product.name}</TableCell>
                  <TableCell>{product.description}</TableCell>
                  <TableCell>{product.unit_price} ر.س</TableCell>
                  <TableCell>{product.tax_rate}%</TableCell>
                  <TableCell>{product.is_service ? 'نعم' : 'لا'}</TableCell>
                  <TableCell className="text-right">
                    <Button variant="ghost" size="sm" onClick={() => openEditModal(product)} className="ml-2">
                      <PencilIcon className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => handleDeleteProduct(product.product_id)}>
                      <TrashIcon className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProductManagement;

