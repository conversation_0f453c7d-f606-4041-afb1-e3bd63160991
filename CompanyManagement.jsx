import React, { useState, useEffect } from 'react';
import { companiesAPI } from '../services/api';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter } from '@/components/ui/dialog';
import { PencilIcon, TrashIcon, PlusIcon } from '@heroicons/react/24/outline';
import { useAuth } from '../contexts/AuthContext';

const CompanyManagement = () => {
  const { user } = useAuth();
  const [companies, setCompanies] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentCompany, setCurrentCompany] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    tax_id: '',
    address: '',
    contact_person: '',
    email: '',
    phone: ''
  });

  useEffect(() => {
    fetchCompanies();
  }, []);

  const fetchCompanies = async () => {
    try {
      setLoading(true);
      const response = await companiesAPI.getAll();
      setCompanies(response.data);
    } catch (err) {
      setError('فشل في جلب بيانات الشركات.');
      console.error('Error fetching companies:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { id, value } = e.target;
    setFormData(prev => ({ ...prev, [id]: value }));
  };

  const handleAddEditCompany = async (e) => {
    e.preventDefault();
    setError('');
    try {
      if (currentCompany) {
        await companiesAPI.update(currentCompany.company_id, formData);
      } else {
        await companiesAPI.create(formData);
      }
      fetchCompanies();
      setIsModalOpen(false);
      setFormData({
        name: '',
        tax_id: '',
        address: '',
        contact_person: '',
        email: '',
        phone: ''
      });
      setCurrentCompany(null);
    } catch (err) {
      setError(err.response?.data?.error || 'فشل في حفظ بيانات الشركة.');
      console.error('Error saving company:', err);
    }
  };

  const handleDeleteCompany = async (companyId) => {
    if (window.confirm('هل أنت متأكد أنك تريد حذف هذه الشركة؟')) {
      setError('');
      try {
        await companiesAPI.delete(companyId);
        fetchCompanies();
      } catch (err) {
        setError(err.response?.data?.error || 'فشل في حذف الشركة.');
        console.error('Error deleting company:', err);
      }
    }
  };

  const openEditModal = (company) => {
    setCurrentCompany(company);
    setFormData({
      name: company.name,
      tax_id: company.tax_id,
      address: company.address,
      contact_person: company.contact_person,
      email: company.email,
      phone: company.phone
    });
    setIsModalOpen(true);
  };

  const openAddModal = () => {
    setCurrentCompany(null);
    setFormData({
      name: '',
      tax_id: '',
      address: '',
      contact_person: '',
      email: '',
      phone: ''
    });
    setIsModalOpen(true);
  };

  if (loading) {
    return <div className="text-center py-8">جاري التحميل...</div>;
  }

  if (error) {
    return <div className="text-center py-8 text-red-500">خطأ: {error}</div>;
  }

  return (
    <div className="container mx-auto p-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-2xl font-bold">إدارة الشركات</CardTitle>
          <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
            <DialogTrigger asChild>
              <Button onClick={openAddModal}>
                <PlusIcon className="h-5 w-5 ml-2" /> إضافة شركة جديدة
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>{currentCompany ? 'تعديل شركة' : 'إضافة شركة جديدة'}</DialogTitle>
                <DialogDescription>
                  {currentCompany ? 'قم بتعديل بيانات الشركة.' : 'قم بإضافة شركة جديدة إلى النظام.'}
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleAddEditCompany} className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="name" className="text-right">الاسم</Label>
                  <Input id="name" value={formData.name} onChange={handleInputChange} className="col-span-3" required />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="tax_id" className="text-right">الرقم الضريبي</Label>
                  <Input id="tax_id" value={formData.tax_id} onChange={handleInputChange} className="col-span-3" required />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="address" className="text-right">العنوان</Label>
                  <Input id="address" value={formData.address} onChange={handleInputChange} className="col-span-3" />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="contact_person" className="text-right">شخص الاتصال</Label>
                  <Input id="contact_person" value={formData.contact_person} onChange={handleInputChange} className="col-span-3" />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="email" className="text-right">البريد الإلكتروني</Label>
                  <Input id="email" type="email" value={formData.email} onChange={handleInputChange} className="col-span-3" />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="phone" className="text-right">رقم الهاتف</Label>
                  <Input id="phone" value={formData.phone} onChange={handleInputChange} className="col-span-3" />
                </div>
                <DialogFooter>
                  <Button type="submit">{currentCompany ? 'حفظ التعديلات' : 'إضافة شركة'}</Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>الاسم</TableHead>
                <TableHead>الرقم الضريبي</TableHead>
                <TableHead>البريد الإلكتروني</TableHead>
                <TableHead>الهاتف</TableHead>
                <TableHead className="text-right">الإجراءات</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {companies.map((company) => (
                <TableRow key={company.company_id}>
                  <TableCell className="font-medium">{company.name}</TableCell>
                  <TableCell>{company.tax_id}</TableCell>
                  <TableCell>{company.email}</TableCell>
                  <TableCell>{company.phone}</TableCell>
                  <TableCell className="text-right">
                    <Button variant="ghost" size="sm" onClick={() => openEditModal(company)} className="ml-2">
                      <PencilIcon className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => handleDeleteCompany(company.company_id)}>
                      <TrashIcon className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default CompanyManagement;

