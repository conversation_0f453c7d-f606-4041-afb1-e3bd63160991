from flask import Blueprint, jsonify, request
from src.models.user import Customer, db

customer_bp = Blueprint('customer', __name__)

@customer_bp.route('/customers', methods=['GET'])
def get_customers():
    """Get all customers, optionally filtered by company"""
    company_id = request.args.get('company_id')
    
    if company_id:
        customers = Customer.query.filter_by(company_id=company_id).all()
    else:
        customers = Customer.query.all()
    
    return jsonify([customer.to_dict() for customer in customers])

@customer_bp.route('/customers', methods=['POST'])
def create_customer():
    """Create a new customer"""
    try:
        data = request.json
        
        # Validate required fields
        required_fields = ['name', 'company_id']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({'error': f'{field} is required'}), 400
        
        customer = Customer(
            company_id=data['company_id'],
            name=data['name'],
            tax_id=data.get('tax_id'),
            address=data.get('address'),
            email=data.get('email'),
            phone=data.get('phone')
        )
        
        db.session.add(customer)
        db.session.commit()
        
        return jsonify(customer.to_dict()), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@customer_bp.route('/customers/<customer_id>', methods=['GET'])
def get_customer(customer_id):
    """Get a specific customer by ID"""
    customer = Customer.query.get_or_404(customer_id)
    return jsonify(customer.to_dict())

@customer_bp.route('/customers/<customer_id>', methods=['PUT'])
def update_customer(customer_id):
    """Update a customer"""
    try:
        customer = Customer.query.get_or_404(customer_id)
        data = request.json
        
        # Update fields if provided
        if 'name' in data:
            customer.name = data['name']
        if 'tax_id' in data:
            customer.tax_id = data['tax_id']
        if 'address' in data:
            customer.address = data['address']
        if 'email' in data:
            customer.email = data['email']
        if 'phone' in data:
            customer.phone = data['phone']
        
        db.session.commit()
        return jsonify(customer.to_dict())
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@customer_bp.route('/customers/<customer_id>', methods=['DELETE'])
def delete_customer(customer_id):
    """Delete a customer"""
    try:
        customer = Customer.query.get_or_404(customer_id)
        db.session.delete(customer)
        db.session.commit()
        return '', 204
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

