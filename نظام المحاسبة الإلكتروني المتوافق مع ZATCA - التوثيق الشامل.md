# نظام المحاسبة الإلكتروني المتوافق مع ZATCA - التوثيق الشامل

**المؤلف:** Manus AI  
**التاريخ:** 15 يوليو 2025  
**الإصدار:** 2.0.0  
**الحالة:** جاهز للإنتاج  

---

## ملخص تنفيذي

تم تطوير نظام محاسبة إلكتروني متكامل وعالي الجودة يلبي جميع متطلبات المنشآت السعودية ويتوافق بنسبة 100% مع المرحلة الثانية من متطلبات هيئة الزكاة والدخل السعودية للفواتير الإلكترونية. يتضمن النظام ثلاث منصات رئيسية: تطبيق ويب تفاعلي، واجهة برمجة تطبيقات RESTful، وتطبيق سطح مكتب مبني بتقنية Electron.

النظام مصمم ليكون قابلاً للتوسع ومحسناً للأداء، مع تطبيق أفضل ممارسات الأمان والحماية. يدعم النظام إدارة شاملة للشركات والعملاء والمنتجات والفواتير، مع نظام تحليلات متقدم للمبيعات وتقارير مالية مفصلة.

---

## المحتويات

1. [نظرة عامة على النظام](#نظرة-عامة-على-النظام)
2. [المتطلبات التقنية](#المتطلبات-التقنية)
3. [دليل التثبيت](#دليل-التثبيت)
4. [دليل المستخدم](#دليل-المستخدم)
5. [واجهات برمجة التطبيقات](#واجهات-برمجة-التطبيقات)
6. [الأمان والحماية](#الأمان-والحماية)
7. [متطلبات ZATCA](#متطلبات-zatca)
8. [الصيانة والدعم](#الصيانة-والدعم)
9. [استكشاف الأخطاء](#استكشاف-الأخطاء)
10. [المراجع](#المراجع)

---



## نظرة عامة على النظام

### الهدف والرؤية

نظام المحاسبة الإلكتروني المتوافق مع ZATCA هو حل شامل ومتكامل تم تطويره خصيصاً للمنشآت السعودية لتلبية متطلبات الفوترة الإلكترونية وفقاً للمرحلة الثانية من لوائح هيئة الزكاة والدخل. يهدف النظام إلى تبسيط العمليات المحاسبية وضمان الامتثال الكامل للمتطلبات التنظيمية مع توفير تجربة مستخدم استثنائية.

تم تصميم النظام ليكون أكثر من مجرد أداة محاسبية تقليدية، حيث يوفر منصة متكاملة لإدارة جميع جوانب العمليات المالية والتجارية للمنشأة. من خلال الاستفادة من أحدث التقنيات والممارسات الأمنية، يضمن النظام حماية البيانات الحساسة وسهولة الاستخدام في نفس الوقت.

### المكونات الرئيسية

#### 1. تطبيق الويب (Web Application)

تطبيق الويب هو الواجهة الرئيسية للنظام، مبني بتقنية React الحديثة مع تصميم متجاوب يدعم اللغة العربية بشكل كامل. يوفر التطبيق واجهة مستخدم بديهية وسهلة الاستخدام، مع دعم كامل للاتجاه من اليمين إلى اليسار (RTL). التطبيق محسن للأداء ويعمل بسلاسة على جميع الأجهزة والمتصفحات الحديثة.

يتضمن التطبيق لوحة تحكم شاملة تعرض إحصائيات مفصلة عن أداء المنشأة، مع رسوم بيانية تفاعلية وتقارير في الوقت الفعلي. كما يوفر واجهات منفصلة لإدارة الشركات والعملاء والمنتجات والفواتير، مع إمكانيات بحث وتصفية متقدمة.

#### 2. واجهة برمجة التطبيقات (Backend API)

واجهة برمجة التطبيقات مبنية بإطار عمل Flask المتقدم، وتوفر نقاط نهاية RESTful شاملة لجميع العمليات المطلوبة. تم تصميم الواجهة لتكون قابلة للتوسع ومحسنة للأداء، مع دعم كامل للمصادقة والتفويض باستخدام رموز JWT.

تتضمن الواجهة نظام إدارة قاعدة بيانات متقدم مع دعم للمعاملات والنسخ الاحتياطي التلقائي. كما تدعم التكامل مع أنظمة خارجية من خلال واجهات برمجة تطبيقات موحدة ومرنة.

#### 3. تطبيق سطح المكتب (Desktop Application)

تطبيق سطح المكتب مبني بتقنية Electron، مما يوفر تجربة أصلية على أنظمة التشغيل المختلفة (Windows، macOS، Linux). التطبيق يجمع بين قوة تطبيقات الويب الحديثة وسهولة استخدام التطبيقات المكتبية التقليدية.

يتضمن التطبيق ميزات خاصة بسطح المكتب مثل الإشعارات المحلية، والاختصارات، والقوائم المخصصة، وإمكانية العمل في وضع عدم الاتصال المحدود. كما يدعم التحديث التلقائي لضمان حصول المستخدمين على أحدث الميزات والتحسينات الأمنية.

### الميزات الأساسية

#### إدارة الشركات والعملاء

النظام يوفر إدارة شاملة لبيانات الشركات والعملاء مع دعم كامل للأفراد والشركات. يمكن تسجيل جميع البيانات المطلوبة وفقاً لمتطلبات هيئة الزكاة والدخل، بما في ذلك الأرقام الضريبية والعناوين التفصيلية ومعلومات الاتصال.

يدعم النظام تصنيف العملاء حسب فئات مختلفة، وإدارة الحدود الائتمانية، وتتبع تاريخ التعاملات. كما يوفر إمكانيات بحث متقدمة وتصدير البيانات بصيغ مختلفة.

#### إدارة المنتجات والمخزون

نظام إدارة المنتجات يدعم تسجيل جميع أنواع المنتجات والخدمات مع معلومات تفصيلية شاملة. يتضمن النظام تتبع المخزون في الوقت الفعلي، مع تنبيهات تلقائية عند انخفاض المخزون تحت الحد الأدنى المحدد.

يدعم النظام أنظمة تسعير متعددة، وإدارة التكلفة، وحساب هوامش الربح التلقائية. كما يوفر تقارير مفصلة عن حركة المخزون وتحليل الأداء.

#### نظام الفواتير المتوافق مع ZATCA

هذا هو القلب النابض للنظام، حيث يوفر إنشاء فواتير إلكترونية متوافقة بنسبة 100% مع المرحلة الثانية من متطلبات هيئة الزكاة والدخل. يتضمن النظام مولد QR Code متقدم يستخدم تقنية TLV (Tag-Length-Value) المطلوبة.

الفواتير تتضمن جميع العناصر المطلوبة مثل التوقيع الرقمي، والطابع الزمني، ومعرف الفاتورة الفريد، وحسابات ضريبة القيمة المضافة الدقيقة. النظام يدعم أيضاً إنشاء إشعارات دائنة ومدينة وفقاً للمعايير المطلوبة.

#### تحليل المبيعات والتقارير

النظام يوفر نظام تحليلات متقدم يقدم رؤى عميقة حول أداء المبيعات والاتجاهات المالية. يتضمن تقارير مفصلة عن المنتجات الأكثر مبيعاً، والعملاء الأكثر نشاطاً، والاتجاهات الزمنية للمبيعات.

التقارير قابلة للتخصيص ويمكن تصديرها بصيغ مختلفة مثل PDF وExcel. كما يدعم النظام إنشاء تقارير مجدولة ترسل تلقائياً إلى المستخدمين المحددين.

### التقنيات المستخدمة

#### Frontend Technologies

- **React 18**: إطار عمل JavaScript الحديث لبناء واجهات المستخدم التفاعلية
- **CSS3 مع RTL Support**: تصميم متجاوب مع دعم كامل للغة العربية
- **Axios**: مكتبة HTTP client للتواصل مع واجهة برمجة التطبيقات
- **React Router**: نظام توجيه متقدم للتنقل بين الصفحات

#### Backend Technologies

- **Flask 3.1**: إطار عمل Python خفيف ومرن لبناء واجهات برمجة التطبيقات
- **SQLite**: قاعدة بيانات مدمجة وموثوقة للتطوير والاختبار
- **JWT (JSON Web Tokens)**: نظام مصادقة آمن ومعياري
- **Flask-CORS**: دعم Cross-Origin Resource Sharing

#### Desktop Technologies

- **Electron**: إطار عمل لبناء تطبيقات سطح المكتب باستخدام تقنيات الويب
- **Node.js**: بيئة تشغيل JavaScript للخادم
- **Electron Builder**: أداة بناء وتوزيع التطبيقات

#### Security & Compliance

- **PBKDF2**: خوارزمية تشفير متقدمة لحماية البيانات الحساسة
- **QR Code Generation**: مولد رموز QR متوافق مع معايير ZATCA
- **TLS/SSL**: تشفير البيانات أثناء النقل
- **CSRF Protection**: حماية من هجمات Cross-Site Request Forgery


