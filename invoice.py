from flask import Blueprint, jsonify, request
from src.models.user import Invoice, InvoiceItem, Product, Customer, db
from decimal import Decimal
from datetime import datetime, date
import uuid

invoice_bp = Blueprint('invoice', __name__)

@invoice_bp.route('/invoices', methods=['GET'])
def get_invoices():
    """Get all invoices, optionally filtered by company"""
    company_id = request.args.get('company_id')
    
    if company_id:
        invoices = Invoice.query.filter_by(company_id=company_id).all()
    else:
        invoices = Invoice.query.all()
    
    return jsonify([invoice.to_dict() for invoice in invoices])

@invoice_bp.route('/invoices', methods=['POST'])
def create_invoice():
    """Create a new invoice"""
    try:
        data = request.json
        
        # Validate required fields
        required_fields = ['company_id', 'customer_id', 'invoice_number']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({'error': f'{field} is required'}), 400
        
        # Check if invoice number already exists
        existing_invoice = Invoice.query.filter_by(invoice_number=data['invoice_number']).first()
        if existing_invoice:
            return jsonify({'error': 'Invoice number already exists'}), 400
        
        # Parse dates
        invoice_date = datetime.strptime(data.get('invoice_date', datetime.now().strftime('%Y-%m-%d')), '%Y-%m-%d').date()
        due_date = None
        if data.get('due_date'):
            due_date = datetime.strptime(data['due_date'], '%Y-%m-%d').date()
        
        invoice = Invoice(
            company_id=data['company_id'],
            customer_id=data['customer_id'],
            invoice_number=data['invoice_number'],
            invoice_date=invoice_date,
            due_date=due_date,
            status=data.get('status', 'draft')
        )
        
        db.session.add(invoice)
        db.session.flush()  # Get the invoice ID
        
        # Add invoice items if provided
        if 'items' in data and data['items']:
            total_amount = Decimal('0')
            tax_amount = Decimal('0')
            
            for item_data in data['items']:
                # Validate item fields
                if not all(k in item_data for k in ['product_id', 'quantity', 'unit_price']):
                    return jsonify({'error': 'Each item must have product_id, quantity, and unit_price'}), 400
                
                # Get product for tax rate
                product = Product.query.get(item_data['product_id'])
                if not product:
                    return jsonify({'error': f'Product {item_data["product_id"]} not found'}), 404
                
                quantity = Decimal(str(item_data['quantity']))
                unit_price = Decimal(str(item_data['unit_price']))
                line_total = quantity * unit_price
                line_tax_amount = line_total * (product.tax_rate / 100)
                
                invoice_item = InvoiceItem(
                    invoice_id=invoice.invoice_id,
                    product_id=item_data['product_id'],
                    quantity=quantity,
                    unit_price=unit_price,
                    line_total=line_total,
                    line_tax_amount=line_tax_amount
                )
                
                db.session.add(invoice_item)
                total_amount += line_total
                tax_amount += line_tax_amount
            
            # Update invoice totals
            invoice.total_amount = total_amount
            invoice.tax_amount = tax_amount
            invoice.grand_total = total_amount + tax_amount
        
        db.session.commit()
        
        return jsonify(invoice.to_dict()), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@invoice_bp.route('/invoices/<invoice_id>', methods=['GET'])
def get_invoice(invoice_id):
    """Get a specific invoice by ID"""
    invoice = Invoice.query.get_or_404(invoice_id)
    return jsonify(invoice.to_dict())

@invoice_bp.route('/invoices/<invoice_id>', methods=['PUT'])
def update_invoice(invoice_id):
    """Update an invoice"""
    try:
        invoice = Invoice.query.get_or_404(invoice_id)
        data = request.json
        
        # Update basic fields
        if 'customer_id' in data:
            invoice.customer_id = data['customer_id']
        if 'invoice_number' in data:
            # Check if new invoice number already exists (excluding current invoice)
            existing_invoice = Invoice.query.filter(
                Invoice.invoice_number == data['invoice_number'],
                Invoice.invoice_id != invoice_id
            ).first()
            if existing_invoice:
                return jsonify({'error': 'Invoice number already exists'}), 400
            invoice.invoice_number = data['invoice_number']
        if 'invoice_date' in data:
            invoice.invoice_date = datetime.strptime(data['invoice_date'], '%Y-%m-%d').date()
        if 'due_date' in data:
            if data['due_date']:
                invoice.due_date = datetime.strptime(data['due_date'], '%Y-%m-%d').date()
            else:
                invoice.due_date = None
        if 'status' in data:
            invoice.status = data['status']
        
        # Update items if provided
        if 'items' in data:
            # Remove existing items
            InvoiceItem.query.filter_by(invoice_id=invoice_id).delete()
            
            total_amount = Decimal('0')
            tax_amount = Decimal('0')
            
            for item_data in data['items']:
                # Validate item fields
                if not all(k in item_data for k in ['product_id', 'quantity', 'unit_price']):
                    return jsonify({'error': 'Each item must have product_id, quantity, and unit_price'}), 400
                
                # Get product for tax rate
                product = Product.query.get(item_data['product_id'])
                if not product:
                    return jsonify({'error': f'Product {item_data["product_id"]} not found'}), 404
                
                quantity = Decimal(str(item_data['quantity']))
                unit_price = Decimal(str(item_data['unit_price']))
                line_total = quantity * unit_price
                line_tax_amount = line_total * (product.tax_rate / 100)
                
                invoice_item = InvoiceItem(
                    invoice_id=invoice.invoice_id,
                    product_id=item_data['product_id'],
                    quantity=quantity,
                    unit_price=unit_price,
                    line_total=line_total,
                    line_tax_amount=line_tax_amount
                )
                
                db.session.add(invoice_item)
                total_amount += line_total
                tax_amount += line_tax_amount
            
            # Update invoice totals
            invoice.total_amount = total_amount
            invoice.tax_amount = tax_amount
            invoice.grand_total = total_amount + tax_amount
        
        db.session.commit()
        return jsonify(invoice.to_dict())
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@invoice_bp.route('/invoices/<invoice_id>', methods=['DELETE'])
def delete_invoice(invoice_id):
    """Delete an invoice"""
    try:
        invoice = Invoice.query.get_or_404(invoice_id)
        db.session.delete(invoice)
        db.session.commit()
        return '', 204
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@invoice_bp.route('/invoices/<invoice_id>/generate-number', methods=['POST'])
def generate_invoice_number(invoice_id):
    """Generate a unique invoice number for an invoice"""
    try:
        invoice = Invoice.query.get_or_404(invoice_id)
        
        # Generate invoice number based on company and date
        company_prefix = invoice.company.tax_id[-4:]  # Last 4 digits of tax ID
        date_prefix = invoice.invoice_date.strftime('%Y%m')
        
        # Find the next sequential number for this company and month
        existing_count = Invoice.query.filter(
            Invoice.company_id == invoice.company_id,
            Invoice.invoice_number.like(f'{company_prefix}-{date_prefix}-%')
        ).count()
        
        next_number = existing_count + 1
        new_invoice_number = f'{company_prefix}-{date_prefix}-{next_number:04d}'
        
        # Ensure uniqueness
        while Invoice.query.filter_by(invoice_number=new_invoice_number).first():
            next_number += 1
            new_invoice_number = f'{company_prefix}-{date_prefix}-{next_number:04d}'
        
        invoice.invoice_number = new_invoice_number
        db.session.commit()
        
        return jsonify({'invoice_number': new_invoice_number})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

