<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام المحاسبة الإلكتروني المتوافق مع ZATCA - نسخة الإنتاج</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .header p {
            color: #7f8c8d;
            font-size: 1.2em;
            margin-bottom: 20px;
        }

        .status-badge {
            display: inline-block;
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 1.1em;
            box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-right: 5px solid #3498db;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .feature-card h3 {
            color: #2c3e50;
            font-size: 1.4em;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .feature-card .icon {
            font-size: 1.5em;
            margin-left: 10px;
            color: #3498db;
        }

        .feature-card p {
            color: #7f8c8d;
            line-height: 1.6;
        }

        .api-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .api-section h2 {
            color: #2c3e50;
            font-size: 2em;
            margin-bottom: 20px;
            text-align: center;
        }

        .api-endpoints {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .endpoint {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
        }

        .endpoint .method {
            font-weight: bold;
            color: #e74c3c;
            margin-left: 10px;
        }

        .endpoint .path {
            color: #2c3e50;
        }

        .test-section {
            text-align: center;
            margin-top: 20px;
        }

        .btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
        }

        .btn.success {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
        }

        .btn.warning {
            background: linear-gradient(45deg, #f39c12, #e67e22);
            box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
        }

        .result-box {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            display: none;
        }

        .footer {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .footer p {
            color: #7f8c8d;
            margin-bottom: 10px;
        }

        .footer .links {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .footer .links a {
            color: #3498db;
            text-decoration: none;
            font-weight: bold;
            transition: color 0.3s ease;
        }

        .footer .links a:hover {
            color: #2980b9;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .api-endpoints {
                grid-template-columns: 1fr;
            }
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏢 نظام المحاسبة الإلكتروني المتوافق مع ZATCA</h1>
            <p>نسخة الإنتاج المحسنة - جاهز للاستخدام في البيئة الحقيقية</p>
            <div class="status-badge">
                ✅ النظام يعمل بكفاءة عالية
            </div>
        </div>

        <div class="features-grid">
            <div class="feature-card">
                <h3><span class="icon">🏢</span>إدارة الشركات والعملاء</h3>
                <p>نظام متكامل لإدارة بيانات الشركات والعملاء مع دعم كامل للأفراد والشركات، وإدارة الحدود الائتمانية والتصنيفات المختلفة.</p>
            </div>

            <div class="feature-card">
                <h3><span class="icon">📦</span>إدارة المنتجات والمخزون</h3>
                <p>إدارة شاملة للمنتجات والخدمات مع تتبع المخزون، والتنبيهات التلقائية عند انخفاض المخزون، ونظام التكلفة والتسعير.</p>
            </div>

            <div class="feature-card">
                <h3><span class="icon">🧾</span>فواتير متوافقة مع ZATCA</h3>
                <p>إنشاء فواتير إلكترونية متوافقة 100% مع المرحلة الثانية من متطلبات هيئة الزكاة والدخل، مع QR Code مشفر وتوقيع رقمي.</p>
            </div>

            <div class="feature-card">
                <h3><span class="icon">📊</span>تحليل المبيعات المتقدم</h3>
                <p>تقارير مفصلة وتحليلات ذكية للمبيعات، مع إحصائيات المنتجات الأكثر مبيعاً، والعملاء الأكثر نشاطاً، والاتجاهات الزمنية.</p>
            </div>

            <div class="feature-card">
                <h3><span class="icon">🔒</span>نظام أمان محسن</h3>
                <p>مصادقة JWT متقدمة، تسجيل جميع النشاطات، أدوار مستخدمين متعددة، وحماية شاملة للبيانات الحساسة.</p>
            </div>

            <div class="feature-card">
                <h3><span class="icon">📝</span>تسجيل النشاطات</h3>
                <p>تتبع شامل لجميع العمليات والتغييرات في النظام، مع تسجيل تفصيلي للمستخدمين والأوقات والعمليات المنفذة.</p>
            </div>
        </div>

        <div class="api-section">
            <h2>🔗 واجهات برمجة التطبيقات (API)</h2>
            
            <div class="api-endpoints">
                <div class="endpoint">
                    <span class="method">GET</span>
                    <span class="path">/api/health</span>
                </div>
                <div class="endpoint">
                    <span class="method">POST</span>
                    <span class="path">/api/auth/login</span>
                </div>
                <div class="endpoint">
                    <span class="method">GET</span>
                    <span class="path">/api/companies</span>
                </div>
                <div class="endpoint">
                    <span class="method">GET</span>
                    <span class="path">/api/customers</span>
                </div>
                <div class="endpoint">
                    <span class="method">GET</span>
                    <span class="path">/api/products</span>
                </div>
                <div class="endpoint">
                    <span class="method">GET</span>
                    <span class="path">/api/invoices</span>
                </div>
                <div class="endpoint">
                    <span class="method">POST</span>
                    <span class="path">/api/invoices</span>
                </div>
                <div class="endpoint">
                    <span class="method">GET</span>
                    <span class="path">/api/analytics/sales</span>
                </div>
            </div>

            <div class="test-section">
                <button class="btn" onclick="testHealth()">🔍 فحص صحة النظام</button>
                <button class="btn success" onclick="seedData()">📊 إضافة بيانات تجريبية</button>
                <button class="btn warning" onclick="createTestInvoice()">🧾 إنشاء فاتورة تجريبية</button>
                <button class="btn" onclick="getSalesAnalytics()">📈 تحليل المبيعات</button>
            </div>

            <div id="result" class="result-box"></div>
        </div>

        <div class="footer">
            <p><strong>نظام المحاسبة الإلكتروني المتوافق مع ZATCA - الإصدار 2.0.0</strong></p>
            <p>تم تطويره بأحدث التقنيات لضمان الامتثال الكامل لمتطلبات هيئة الزكاة والدخل السعودية</p>
            <div class="links">
                <a href="#" onclick="testHealth()">فحص النظام</a>
                <a href="#" onclick="showDocumentation()">التوثيق</a>
                <a href="#" onclick="showSupport()">الدعم الفني</a>
                <a href="#" onclick="showContact()">اتصل بنا</a>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = window.location.origin;
        
        function showResult(data, isError = false) {
            const resultBox = document.getElementById('result');
            resultBox.style.display = 'block';
            resultBox.style.background = isError ? '#e74c3c' : '#2c3e50';
            resultBox.textContent = typeof data === 'object' ? JSON.stringify(data, null, 2) : data;
            resultBox.scrollTop = 0;
        }

        function showLoading(button) {
            const originalText = button.innerHTML;
            button.innerHTML = '<span class="loading"></span> جاري التحميل...';
            button.disabled = true;
            return originalText;
        }

        function hideLoading(button, originalText) {
            button.innerHTML = originalText;
            button.disabled = false;
        }

        async function testHealth() {
            const button = event.target;
            const originalText = showLoading(button);
            
            try {
                const response = await fetch(`${API_BASE}/api/health`);
                const data = await response.json();
                showResult(data);
            } catch (error) {
                showResult(`خطأ في الاتصال: ${error.message}`, true);
            } finally {
                hideLoading(button, originalText);
            }
        }

        async function seedData() {
            const button = event.target;
            const originalText = showLoading(button);
            
            try {
                const response = await fetch(`${API_BASE}/api/seed-data`, {
                    method: 'POST'
                });
                const data = await response.json();
                showResult(data);
            } catch (error) {
                showResult(`خطأ في إضافة البيانات: ${error.message}`, true);
            } finally {
                hideLoading(button, originalText);
            }
        }

        async function createTestInvoice() {
            const button = event.target;
            const originalText = showLoading(button);
            
            const invoiceData = {
                customer_id: 1,
                invoice_date: new Date().toISOString().split('T')[0],
                due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                items: [
                    {
                        product_id: 1,
                        description: "خدمة استشارية متخصصة",
                        quantity: 2,
                        unit_price: 1000.00,
                        tax_rate: 15.0
                    },
                    {
                        product_id: 2,
                        description: "برنامج محاسبة احترافي",
                        quantity: 1,
                        unit_price: 5000.00,
                        tax_rate: 15.0
                    }
                ]
            };
            
            try {
                const response = await fetch(`${API_BASE}/api/invoices`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(invoiceData)
                });
                const data = await response.json();
                showResult(data);
            } catch (error) {
                showResult(`خطأ في إنشاء الفاتورة: ${error.message}`, true);
            } finally {
                hideLoading(button, originalText);
            }
        }

        async function getSalesAnalytics() {
            const button = event.target;
            const originalText = showLoading(button);
            
            try {
                const response = await fetch(`${API_BASE}/api/analytics/sales`);
                const data = await response.json();
                showResult(data);
            } catch (error) {
                showResult(`خطأ في جلب التحليلات: ${error.message}`, true);
            } finally {
                hideLoading(button, originalText);
            }
        }

        function showDocumentation() {
            showResult(`
📚 توثيق النظام:

🔗 نقاط النهاية الرئيسية:
- GET /api/health - فحص صحة النظام
- POST /api/auth/login - تسجيل الدخول
- GET /api/companies - جلب الشركات
- GET /api/customers - جلب العملاء
- GET /api/products - جلب المنتجات
- GET /api/invoices - جلب الفواتير
- POST /api/invoices - إنشاء فاتورة جديدة
- GET /api/analytics/sales - تحليل المبيعات

🔐 المصادقة:
- استخدم POST /api/auth/login للحصول على JWT token
- أضف Header: Authorization: Bearer <token>

📋 مثال إنشاء فاتورة:
{
  "customer_id": 1,
  "items": [
    {
      "description": "منتج أو خدمة",
      "quantity": 1,
      "unit_price": 100.00,
      "tax_rate": 15.0
    }
  ]
}
            `);
        }

        function showSupport() {
            showResult(`
🛠️ الدعم الفني:

📧 البريد الإلكتروني: <EMAIL>
📞 الهاتف: +966112345678
💬 الدردشة المباشرة: متاحة 24/7

🕐 ساعات العمل:
- الأحد إلى الخميس: 8:00 ص - 6:00 م
- الجمعة والسبت: 10:00 ص - 4:00 م

🔧 الخدمات المتاحة:
- تثبيت وإعداد النظام
- التدريب على الاستخدام
- الدعم الفني المستمر
- التحديثات والصيانة
- استشارات ZATCA
            `);
        }

        function showContact() {
            showResult(`
📞 معلومات الاتصال:

🏢 شركة التقنية المتقدمة
📍 العنوان: الرياض، المملكة العربية السعودية
📧 البريد الإلكتروني: <EMAIL>
📞 الهاتف: +966112345678
📠 الفاكس: +966112345679

🌐 المواقع الإلكترونية:
- الموقع الرسمي: www.techcompany.sa
- البوابة الإلكترونية: portal.techcompany.sa
- مركز المساعدة: help.techcompany.sa

📱 وسائل التواصل الاجتماعي:
- تويتر: @TechCompanySA
- لينكد إن: TechCompany-SA
- واتساب: +966501234567
            `);
        }

        // تشغيل فحص صحة النظام عند تحميل الصفحة
        window.addEventListener('load', function() {
            setTimeout(testHealth, 1000);
        });
    </script>
</body>
</html>

