from flask import Blueprint, jsonify, request
from src.models.user import Product, db

product_bp = Blueprint('product', __name__)

@product_bp.route('/products', methods=['GET'])
def get_products():
    """Get all products, optionally filtered by company"""
    company_id = request.args.get('company_id')
    
    if company_id:
        products = Product.query.filter_by(company_id=company_id).all()
    else:
        products = Product.query.all()
    
    return jsonify([product.to_dict() for product in products])

@product_bp.route('/products', methods=['POST'])
def create_product():
    """Create a new product"""
    try:
        data = request.json
        
        # Validate required fields
        required_fields = ['name', 'company_id', 'unit_price']
        for field in required_fields:
            if field not in data or data[field] is None:
                return jsonify({'error': f'{field} is required'}), 400
        
        product = Product(
            company_id=data['company_id'],
            name=data['name'],
            description=data.get('description'),
            unit_price=data['unit_price'],
            tax_rate=data.get('tax_rate', 15.0),  # Default VAT rate in Saudi Arabia
            is_service=data.get('is_service', False)
        )
        
        db.session.add(product)
        db.session.commit()
        
        return jsonify(product.to_dict()), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@product_bp.route('/products/<product_id>', methods=['GET'])
def get_product(product_id):
    """Get a specific product by ID"""
    product = Product.query.get_or_404(product_id)
    return jsonify(product.to_dict())

@product_bp.route('/products/<product_id>', methods=['PUT'])
def update_product(product_id):
    """Update a product"""
    try:
        product = Product.query.get_or_404(product_id)
        data = request.json
        
        # Update fields if provided
        if 'name' in data:
            product.name = data['name']
        if 'description' in data:
            product.description = data['description']
        if 'unit_price' in data:
            product.unit_price = data['unit_price']
        if 'tax_rate' in data:
            product.tax_rate = data['tax_rate']
        if 'is_service' in data:
            product.is_service = data['is_service']
        
        db.session.commit()
        return jsonify(product.to_dict())
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@product_bp.route('/products/<product_id>', methods=['DELETE'])
def delete_product(product_id):
    """Delete a product"""
    try:
        product = Product.query.get_or_404(product_id)
        db.session.delete(product)
        db.session.commit()
        return '', 204
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

