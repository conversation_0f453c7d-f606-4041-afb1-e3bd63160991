# تصميم هيكل النظام وقاعدة البيانات لبرنامج المحاسبة

## 1. هيكل النظام العام (System Architecture)

لتلبية متطلبات برنامج المحاسبة الذي يشمل تطبيق سطح مكتب وتطبيق ويب، بالإضافة إلى الامتثال لمتطلبات الفاتورة الإلكترونية للمرحلة الثانية من هيئة الزكاة والضريبة والجمارك (ZATCA)، سيتم اعتماد هيكل نظام متعدد الطبقات (Multi-tier Architecture) مع فصل واضح بين الواجهات الأمامية (Frontend) والواجهة الخلفية (Backend) وقاعدة البيانات.

### 1.1. المكونات الرئيسية:

*   **الواجهة الأمامية (Frontend):**
    *   **تطبيق الويب (Web Application):** واجهة المستخدم التي يمكن الوصول إليها عبر المتصفح، مخصصة للمحاسبين والإدارات العليا لإدارة العمليات المحاسبية وإنشاء الفواتير.
    *   **تطبيق سطح المكتب (Desktop Application):** تطبيق محلي يوفر تجربة مستخدم غنية وأداءً عاليًا، وقد يستخدم لمهام محددة تتطلب الوصول المباشر لموارد الجهاز أو لتقديم واجهة بديلة في حال عدم توفر اتصال إنترنت مستقر.

*   **الواجهة الخلفية (Backend - API Layer):**
    *   **واجهة برمجة التطبيقات (API):** نقطة الاتصال المركزية لجميع الواجهات الأمامية (الويب وسطح المكتب). ستكون مسؤولة عن معالجة منطق الأعمال (Business Logic)، والتحقق من صحة البيانات، والتفاعل مع قاعدة البيانات، والتعامل مع متطلبات الفاتورة الإلكترونية (ZATCA).
    *   **خدمة الفاتورة الإلكترونية (E-invoicing Service):** مكون مخصص ضمن الواجهة الخلفية أو خدمة منفصلة تتولى مسؤولية توليد الفواتير بصيغة XML أو PDF/A3، وتوقيعها رقميًا، وإرسالها إلى منصة فاتورة التابعة لـ ZATCA، واستقبال الردود.

*   **قاعدة البيانات (Database Layer):**
    *   **قاعدة بيانات علائقية (Relational Database):** لتخزين جميع البيانات المحاسبية مثل بيانات العملاء، الموردين، المنتجات، الحسابات، القيود اليومية، الفواتير، والتقارير.

### 1.2. تدفق البيانات (Data Flow):

1.  يقوم المستخدم (محاسب أو إدارة عليا) بالتفاعل مع تطبيق الويب أو تطبيق سطح المكتب.
2.  ترسل الواجهة الأمامية طلبات (Requests) إلى الواجهة الخلفية (API) عبر بروتوكول HTTP/HTTPS.
3.  تقوم الواجهة الخلفية بمعالجة الطلب، وتطبيق منطق الأعمال، والتفاعل مع قاعدة البيانات لجلب أو حفظ البيانات.
4.  في حالة إنشاء فاتورة، تتفاعل الواجهة الخلفية مع خدمة الفاتورة الإلكترونية لضمان الامتثال لمتطلبات ZATCA.
5.  تعيد الواجهة الخلفية الاستجابة (Response) إلى الواجهة الأمامية.
6.  تقوم الواجهة الأمامية بعرض البيانات للمستخدم.

## 2. تحديد التقنيات المستخدمة (Technology Stack)

لضمان الأداء، قابلية التوسع، وسهولة الصيانة، بالإضافة إلى تلبية المتطلبات الخاصة بالفاتورة الإلكترونية، سيتم اختيار التقنيات التالية:

### 2.1. الواجهة الخلفية (Backend):

*   **لغة البرمجة:** Python (بايثون) - لمرونتها، مكتباتها الغنية، ومجتمعها الكبير، مما يسهل تطوير منطق الأعمال المعقد والتعامل مع متطلبات ZATCA.
*   **إطار العمل (Framework):** Django أو Flask - كلاهما يوفر أدوات قوية لبناء واجهات برمجة تطبيقات RESTful.
    *   **Django:** مناسب للمشاريع الكبيرة والمعقدة التي تتطلب إدارة قاعدة بيانات متكاملة ولوحة تحكم إدارية جاهزة.
    *   **Flask:** أخف وزنًا وأكثر مرونة، مناسب لواجهات برمجة التطبيقات الصغيرة والمتوسطة أو الخدمات المصغرة.
    *   **القرار المبدئي:** سنميل نحو **Django** نظرًا لشموليته وقدرته على التعامل مع نظام محاسبي متكامل.
*   **خادم الويب (Web Server):** Gunicorn (للتطبيق) و Nginx (كـ Reverse Proxy) - لتحقيق الأداء العالي وتوزيع الأحمال.

### 2.2. قاعدة البيانات (Database):

*   **نظام إدارة قواعد البيانات (DBMS):** PostgreSQL - لقوته، موثوقيته، دعمه للميزات المتقدمة، وقدرته على التعامل مع كميات كبيرة من البيانات بشكل فعال.

### 2.3. الواجهة الأمامية (Frontend):

*   **تطبيق الويب (Web Application):
    *   **إطار عمل JavaScript:** React.js أو Vue.js - لإنشاء واجهات مستخدم تفاعلية وسريعة.
        *   **React.js:** مدعوم من فيسبوك، مجتمع كبير، مرونة عالية.
        *   **Vue.js:** سهل التعلم، أداء جيد، مناسب للمشاريع المتوسطة.
        *   **القرار المبدئي:** سنختار **React.js** لانتشاره الواسع وتوفر الموارد والمكتبات.
    *   **إدارة الحالة (State Management):** Redux (مع React) أو Vuex (مع Vue) - لإدارة حالة التطبيق المعقدة.
    *   **تصميم الواجهة (UI/UX Framework):** Bootstrap أو Material-UI - لتسريع عملية التصميم وتوفير مكونات جاهزة.

*   **تطبيق سطح المكتب (Desktop Application):
    *   **إطار العمل:** Electron.js - يسمح ببناء تطبيقات سطح المكتب باستخدام تقنيات الويب (HTML, CSS, JavaScript)، مما يسهل مشاركة الكود بين تطبيق الويب وسطح المكتب.
    *   **بديل:** PyQt أو Kivy (باستخدام Python) - إذا كانت هناك حاجة لتطبيق مكتوب بالكامل بلغة Python.
    *   **القرار المبدئي:** سنختار **Electron.js** للاستفادة من الكود المشترك مع تطبيق الويب.

### 2.4. متطلبات الفاتورة الإلكترونية (ZATCA Compliance):

*   **توليد XML/PDF/A3:** استخدام مكتبات Python متخصصة لتوليد ملفات XML و PDF/A3 وفقًا للمواصفات الفنية لـ ZATCA.
*   **التوقيع الرقمي (Digital Signature):** استخدام مكتبات تشفير لتوقيع الفواتير رقميًا باستخدام شهادة التوقيع الرقمي المعتمدة من ZATCA.
*   **الاتصال بـ ZATCA API:** استخدام مكتبات HTTP لإجراء طلبات (Requests) إلى واجهة برمجة تطبيقات ZATCA لإرسال الفواتير واستقبال الردود.

## 3. تصميم مخطط قاعدة البيانات (Database Schema)

سيتم تصميم قاعدة بيانات علائقية لتمثيل الكيانات الرئيسية في نظام المحاسبة. فيما يلي مخطط مبدئي للجداول الرئيسية والعلاقات بينها:

### 3.1. الجداول الرئيسية (Core Tables):

*   **`Companies` (الشركات):**
    *   `company_id` (PK, UUID)
    *   `name` (اسم الشركة)
    *   `tax_id` (الرقم الضريبي)
    *   `address` (العنوان)
    *   `contact_person` (شخص الاتصال)
    *   `email` (البريد الإلكتروني)
    *   `phone` (رقم الهاتف)
    *   `created_at`
    *   `updated_at`

*   **`Users` (المستخدمون):**
    *   `user_id` (PK, UUID)
    *   `company_id` (FK to Companies)
    *   `username`
    *   `password_hash`
    *   `email`
    *   `role` (الدور: محاسب، إدارة عليا، إلخ.)
    *   `is_active`
    *   `created_at`
    *   `updated_at`

*   **`Customers` (العملاء):**
    *   `customer_id` (PK, UUID)
    *   `company_id` (FK to Companies)
    *   `name` (اسم العميل)
    *   `tax_id` (الرقم الضريبي للعميل - اختياري)
    *   `address`
    *   `email`
    *   `phone`
    *   `created_at`
    *   `updated_at`

*   **`Suppliers` (الموردون):**
    *   `supplier_id` (PK, UUID)
    *   `company_id` (FK to Companies)
    *   `name` (اسم المورد)
    *   `tax_id` (الرقم الضريبي للمورد - اختياري)
    *   `address`
    *   `email`
    *   `phone`
    *   `created_at`
    *   `updated_at`

*   **`Products` (المنتجات/الخدمات):**
    *   `product_id` (PK, UUID)
    *   `company_id` (FK to Companies)
    *   `name` (اسم المنتج/الخدمة)
    *   `description` (الوصف)
    *   `unit_price` (سعر الوحدة)
    *   `tax_rate` (نسبة الضريبة)
    *   `is_service` (هل هو خدمة؟)
    *   `created_at`
    *   `updated_at`

*   **`Invoices` (الفواتير - المبيعات):**
    *   `invoice_id` (PK, UUID)
    *   `company_id` (FK to Companies)
    *   `customer_id` (FK to Customers)
    *   `invoice_number` (رقم الفاتورة - فريد)
    *   `invoice_date` (تاريخ الفاتورة)
    *   `due_date` (تاريخ الاستحقاق)
    *   `total_amount` (المبلغ الإجمالي قبل الضريبة)
    *   `tax_amount` (مبلغ الضريبة)
    *   `grand_total` (المبلغ الإجمالي بعد الضريبة)
    *   `status` (الحالة: مسودة، صادرة، مدفوعة، ملغاة)
    *   `zatca_status` (حالة الربط مع ZATCA: معلقة، مرسلة، مقبولة، مرفوضة)
    *   `zatca_uuid` (معرف الفاتورة في ZATCA)
    *   `qr_code_data` (بيانات رمز الاستجابة السريعة)
    *   `xml_data` (بيانات الفاتورة بصيغة XML)
    *   `pdf_a3_path` (مسار ملف PDF/A3)
    *   `created_at`
    *   `updated_at`

*   **`Invoice_Items` (بنود الفاتورة):**
    *   `item_id` (PK, UUID)
    *   `invoice_id` (FK to Invoices)
    *   `product_id` (FK to Products)
    *   `quantity` (الكمية)
    *   `unit_price` (سعر الوحدة عند البيع)
    *   `line_total` (إجمالي البند قبل الضريبة)
    *   `line_tax_amount` (مبلغ الضريبة للبند)
    *   `created_at`
    *   `updated_at`

### 3.2. جداول إضافية (Additional Tables - للتوسع المستقبلي):

*   **`Accounts` (الحسابات - شجرة الحسابات):**
    *   `account_id` (PK, UUID)
    *   `company_id` (FK to Companies)
    *   `account_name`
    *   `account_type` (أصول، خصوم، حقوق ملكية، إيرادات، مصروفات)
    *   `parent_account_id` (FK to Accounts - للعلاقات الهرمية)

*   **`Journal_Entries` (القيود اليومية):**
    *   `entry_id` (PK, UUID)
    *   `company_id` (FK to Companies)
    *   `entry_date`
    *   `description`
    *   `created_at`
    *   `updated_at`

*   **`Journal_Items` (بنود القيد اليومي):**
    *   `journal_item_id` (PK, UUID)
    *   `entry_id` (FK to Journal_Entries)
    *   `account_id` (FK to Accounts)
    *   `debit` (مدين)
    *   `credit` (دائن)

*   **`Payments` (المدفوعات):**
    *   `payment_id` (PK, UUID)
    *   `invoice_id` (FK to Invoices)
    *   `payment_date`
    *   `amount`
    *   `payment_method`

### 3.3. العلاقات (Relationships):

*   `Companies` 1:M `Users`
*   `Companies` 1:M `Customers`
*   `Companies` 1:M `Suppliers`
*   `Companies` 1:M `Products`
*   `Companies` 1:M `Invoices`
*   `Invoices` 1:M `Invoice_Items`
*   `Customers` 1:M `Invoices`
*   `Products` 1:M `Invoice_Items`
*   `Invoices` 1:M `Payments`
*   `Companies` 1:M `Accounts`
*   `Companies` 1:M `Journal_Entries`
*   `Journal_Entries` 1:M `Journal_Items`
*   `Accounts` 1:M `Journal_Items`

---

