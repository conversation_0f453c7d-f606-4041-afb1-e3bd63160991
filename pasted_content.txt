# Comprehensive ERP Accounting System Prompts

## 1. Basic Prompt for the General System

```
You are an experienced ERP accounting system developer. I want to create an integrated accounting system that includes:

**Basic Modules:**
- Accounts and ledger management
- Customer and supplier management
- Inventory and purchasing management
- Sales and invoice management
- Financial and accounting reports
- Treasury and banking management

**Technical Requirements:**
- A modern and easy-to-use user interface
- A secure and performance-optimized database
- An advanced permissions system
- Arabic and English language support
- Customizable and exportable reports

**Accounting Standards:**
- Follows International Financial Reporting Standards (IFRS)
- Supports local accounting standards
- Double-entry bookkeeping
- Audit and review tracking

Start by designing the overall structure of the system and database. ```

## 2. Prompt Accounts Module

```
Design the accounting module, which includes:

**Chart of Accounts:**
- Tree structure for accounts (assets, liabilities, equity, revenues, expenses)
- Automatic account numbering
- Ability to create multi-level sub-accounts
- Link accounts to different currencies

**Accounting Entries:**
- Manual and automatic entry
- Approvals and credits system
- Monthly and annual closing entries
- Linking entries to supporting documents

**Reports:**
- Trial Balance
- Income Statement
- Balance Sheet
- Cash Flow Statement
- General and Subordinate Ledger

Create the required database models and interfaces.
```

## 3. Prompt Customer and Supplier Module

```
Develop a Customer and Supplier Management Module with the following features:

**Customer/Supplier Data:**
- Comprehensive Personal and Financial Information
- Multiple Classifications and Categories
- Credit Limits and Payment Terms
- Transaction History Tracking

**Current Accounts:**
- Detailed Account Statements
- Aging of Debts and Receivables
- Account Reconciliations
- Automatic Due Notices

**Reports:**
- Customer/Supplier Statement
- Debt Aging Report
- Customer and Supplier Analysis
- Sales and Purchase Statistics

**Additional Features:**
- Customer Loyalty Points System
- Credit Risk Assessment
- Contract and Agreement Management
- Integration with CRM Systems

Design the required database and user interfaces. ```

## 4. Prompt Inventory Module

```
Create a comprehensive inventory management module that includes:

**Item Management:**
- Comprehensive catalog of products and services
- Item hierarchies
- Multiple units of measure and conversion factors
- Tiered prices and multiple currencies

**Inventory Movement:**
- Receiving and disbursing goods
- Inter-warehouse transfers
- Periodic inventory adjustments
- Tracking run and expiration numbers

**Inventory Valuation Methods:**
- Weighted Average
- First In, First Out (FIFO)
- Last In, First Out (LIFO)
- Specific Costing

**Reports and Analytics:**
- Item Movement Report
- Inventory Turnover Analysis
- Slow-Moving Items Report
- Reorder Levels Report

**Advanced Features:**
- Barcode and QR Code
- Automatic Inventory Alerts
- Location Tracking within Warehouses
- Integration with Point of Sale

The system is designed with modern interfaces and an enhanced database. ```

## 5. Prompt Sales and Invoicing Module

```
Develop an integrated sales and invoicing module with:

**Sales Management:**
- Quotations and Orders
- Tracking Business Opportunities
- Sales Team Management
- Sales Goals and Rewards

**Invoices and Receipts:**
- Multi-format sales invoices
- Approved Tax Invoices
- Credit and Debit Notes
- Recurring and Recurring Invoices

**Payment Systems:**
- Recording Receivables
- Integration with Electronic Banks
- Installment and Deferred Payment
- Discount and Offer System

**Analytics and Reports:**
- Detailed Sales Reports
- Performance Analysis by Product/Customer
- Sales Forecasts
- Interactive Dashboard

**Tax Compliance:**
- VAT Support
- Required Tax Reports
- Integration with Government Systems
- Electronic Document Archiving

Focus on ease of use, security, and legal compliance. ```

## 6. Prompt Reports and Analytics

```
Design an advanced reporting system that includes:

**Basic Financial Reports:**
- Balance Sheet
- Statement of Comprehensive Income
- Statement of Cash Flows
- Statement of Changes in Equity
- Supplementary Notes

**Management Reports:**
- Profitability Analysis by Product/Customer
- Comparative Performance Reports
- Key Performance Indicators (KPIs)
- Financial Forecast Reports

**Operational Reports:**
- Sales and Purchase Reports
- Inventory and Transaction Reports
- Customer and Supplier Reports
- Treasury and Bank Reports

**Advanced Features:**
- Customizable Report Design
- Scheduling and Automated Reporting
- Exporting to Multiple Formats (PDF, Excel, CSV)
- Interactive Dashboards

**Graphic Visualizations:**
- Charts and Statistics
- Data Heat Maps
- Timeline and Reference Comparisons
- Predictive Analytics

Make the system easy to use and flexible for customization.
```

## 7. Security and Authorization Prompt

```
Establish a comprehensive security and authorization system that includes:

**User Management:**
- Detailed user profiles
- User groups and roles
- Flexible and tiered authorizations
- Secure login (2FA)

**Data Security:**
- Encryption of sensitive data
- Automatic backups
- Comprehensive audit logs
- Protection from hacks and viruses

**Monitoring and Auditing:**
- Tracking of all processes and changes
- Activity history
- Audit and audit reports
- Suspicious activity alerts

**Compliance and Standards:**
- Adherence to SOX auditing standards
- Compliance with data protection laws
- Data retention policies
- Emergency and recovery procedures

**Secure Interfaces:**
- Secure and multi-factor login
- Protected and time-limited sessions
- Communication encryption (SSL/TLS)
- Protection from common web attacks