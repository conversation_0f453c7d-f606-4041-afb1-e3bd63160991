import base64
import hashlib
import hmac
import json
import qrcode
import xml.etree.ElementTree as ET
from datetime import datetime
from io import BytesIO
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives.serialization import pkcs12
import requests

from security_enhancements import DataValidation

class ZATCAService:
    """
    خدمة التكامل مع هيئة الزكاة والدخل السعودية (ZATCA)
    للفواتير الإلكترونية - المرحلة الثانية
    """
    
    def __init__(self, config=None):
        self.config = config or {
            'environment': 'sandbox',  # sandbox أو production
            'api_base_url': 'https://gw-fatoora.zatca.gov.sa/e-invoicing/developer-portal',
            'compliance_api_url': 'https://gw-fatoora.zatca.gov.sa/e-invoicing/developer-portal/compliance',
            'production_api_url': 'https://gw-fatoora.zatca.gov.sa/e-invoicing/core',
        }
        
    def generate_qr_code(self, invoice_data):
        """
        إنشاء QR Code للفاتورة وفقاً لمعايير ZATCA
        """
        try:
            # البيانات المطلوبة في QR Code
            qr_data = self._prepare_qr_data(invoice_data)
            
            # تشفير البيانات بـ Base64
            qr_string = self._encode_qr_data(qr_data)
            
            # إنشاء QR Code
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(qr_string)
            qr.make(fit=True)
            
            # تحويل إلى صورة
            img = qr.make_image(fill_color="black", back_color="white")
            
            # تحويل إلى Base64 للحفظ
            buffer = BytesIO()
            img.save(buffer, format='PNG')
            qr_image_base64 = base64.b64encode(buffer.getvalue()).decode()
            
            return {
                'qr_code_data': qr_string,
                'qr_code_image': qr_image_base64,
                'success': True
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'فشل في إنشاء QR Code: {str(e)}'
            }
    
    def _prepare_qr_data(self, invoice_data):
        """
        تحضير البيانات المطلوبة في QR Code
        """
        return {
            'seller_name': invoice_data.get('company_name', ''),
            'vat_registration_number': invoice_data.get('company_tax_id', ''),
            'timestamp': invoice_data.get('invoice_date', ''),
            'invoice_total': str(invoice_data.get('grand_total', 0)),
            'vat_total': str(invoice_data.get('tax_amount', 0))
        }
    
    def _encode_qr_data(self, qr_data):
        """
        تشفير بيانات QR Code وفقاً لمعايير ZATCA
        """
        # تحويل البيانات إلى التنسيق المطلوب
        encoded_data = []
        
        # اسم البائع
        encoded_data.append(self._encode_tlv(1, qr_data['seller_name']))
        
        # الرقم الضريبي
        encoded_data.append(self._encode_tlv(2, qr_data['vat_registration_number']))
        
        # الطابع الزمني
        encoded_data.append(self._encode_tlv(3, qr_data['timestamp']))
        
        # إجمالي الفاتورة
        encoded_data.append(self._encode_tlv(4, qr_data['invoice_total']))
        
        # إجمالي الضريبة
        encoded_data.append(self._encode_tlv(5, qr_data['vat_total']))
        
        # دمج البيانات وتشفيرها بـ Base64
        combined_data = b''.join(encoded_data)
        return base64.b64encode(combined_data).decode()
    
    def _encode_tlv(self, tag, value):
        """
        تشفير البيانات بتنسيق TLV (Tag-Length-Value)
        """
        value_bytes = value.encode('utf-8')
        length = len(value_bytes)
        return bytes([tag, length]) + value_bytes
    
    def generate_invoice_hash(self, invoice_xml):
        """
        إنشاء hash للفاتورة
        """
        try:
            # تحويل XML إلى bytes
            xml_bytes = invoice_xml.encode('utf-8')
            
            # إنشاء SHA-256 hash
            hash_object = hashlib.sha256(xml_bytes)
            invoice_hash = hash_object.hexdigest()
            
            return {
                'hash': invoice_hash,
                'success': True
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'فشل في إنشاء hash للفاتورة: {str(e)}'
            }
    
    def generate_invoice_xml(self, invoice_data):
        """
        إنشاء XML للفاتورة وفقاً لمعايير UBL 2.1
        """
        try:
            # إنشاء العنصر الجذر
            root = ET.Element("Invoice")
            root.set("xmlns", "urn:oasis:names:specification:ubl:schema:xsd:Invoice-2")
            root.set("xmlns:cac", "urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2")
            root.set("xmlns:cbc", "urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2")
            
            # معلومات أساسية
            ET.SubElement(root, "cbc:ID").text = invoice_data.get('invoice_number', '')
            ET.SubElement(root, "cbc:IssueDate").text = invoice_data.get('invoice_date', '')
            ET.SubElement(root, "cbc:InvoiceTypeCode").text = "388"  # Commercial Invoice
            
            # معلومات البائع
            supplier_party = ET.SubElement(root, "cac:AccountingSupplierParty")
            supplier = ET.SubElement(supplier_party, "cac:Party")
            
            supplier_name = ET.SubElement(supplier, "cac:PartyName")
            ET.SubElement(supplier_name, "cbc:Name").text = invoice_data.get('company_name', '')
            
            # الرقم الضريبي للبائع
            supplier_tax = ET.SubElement(supplier, "cac:PartyTaxScheme")
            ET.SubElement(supplier_tax, "cbc:CompanyID").text = invoice_data.get('company_tax_id', '')
            
            # معلومات المشتري
            customer_party = ET.SubElement(root, "cac:AccountingCustomerParty")
            customer = ET.SubElement(customer_party, "cac:Party")
            
            customer_name = ET.SubElement(customer, "cac:PartyName")
            ET.SubElement(customer_name, "cbc:Name").text = invoice_data.get('customer_name', '')
            
            # بنود الفاتورة
            for item in invoice_data.get('items', []):
                invoice_line = ET.SubElement(root, "cac:InvoiceLine")
                ET.SubElement(invoice_line, "cbc:ID").text = str(item.get('line_number', 1))
                
                # الكمية
                quantity = ET.SubElement(invoice_line, "cbc:InvoicedQuantity")
                quantity.set("unitCode", "PCE")
                quantity.text = str(item.get('quantity', 1))
                
                # المبلغ
                line_amount = ET.SubElement(invoice_line, "cbc:LineExtensionAmount")
                line_amount.set("currencyID", "SAR")
                line_amount.text = str(item.get('total_amount', 0))
                
                # معلومات المنتج
                item_element = ET.SubElement(invoice_line, "cac:Item")
                ET.SubElement(item_element, "cbc:Name").text = item.get('product_name', '')
                
                # السعر
                price = ET.SubElement(invoice_line, "cac:Price")
                price_amount = ET.SubElement(price, "cbc:PriceAmount")
                price_amount.set("currencyID", "SAR")
                price_amount.text = str(item.get('unit_price', 0))
            
            # الإجماليات
            tax_total = ET.SubElement(root, "cac:TaxTotal")
            tax_amount = ET.SubElement(tax_total, "cbc:TaxAmount")
            tax_amount.set("currencyID", "SAR")
            tax_amount.text = str(invoice_data.get('tax_amount', 0))
            
            legal_total = ET.SubElement(root, "cac:LegalMonetaryTotal")
            payable_amount = ET.SubElement(legal_total, "cbc:PayableAmount")
            payable_amount.set("currencyID", "SAR")
            payable_amount.text = str(invoice_data.get('grand_total', 0))
            
            # تحويل إلى string
            xml_string = ET.tostring(root, encoding='unicode')
            
            return {
                'xml': xml_string,
                'success': True
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'فشل في إنشاء XML للفاتورة: {str(e)}'
            }
    
    def validate_invoice(self, invoice_data):
        """
        التحقق من صحة بيانات الفاتورة وفقاً لمعايير ZATCA
        """
        errors = []
        
        # التحقق من البيانات الأساسية
        if not invoice_data.get('company_name'):
            errors.append('اسم الشركة مطلوب')
            
        company_tax_id = invoice_data.get('company_tax_id')
        if not company_tax_id:
            errors.append('الرقم الضريبي للشركة مطلوب')
        elif not DataValidation.validate_tax_id(company_tax_id):
            errors.append('صيغة الرقم الضريبي للشركة غير صحيحة')
            
        if not invoice_data.get('customer_name'):
            errors.append('اسم العميل مطلوب')
            
        if not invoice_data.get('invoice_number'):
            errors.append('رقم الفاتورة مطلوب')
            
        if not invoice_data.get('invoice_date'):
            errors.append('تاريخ الفاتورة مطلوب')
            
        # التحقق من البنود
        items = invoice_data.get('items', [])
        if not items:
            errors.append('يجب أن تحتوي الفاتورة على بند واحد على الأقل')
        
        for i, item in enumerate(items):
            if not item.get('product_name'):
                errors.append(f'اسم المنتج مطلوب في البند {i+1}')
                
            if not item.get('quantity') or item.get('quantity') <= 0:
                errors.append(f'الكمية يجب أن تكون أكبر من صفر في البند {i+1}')
                
            if not item.get('unit_price') or item.get('unit_price') <= 0:
                errors.append(f'سعر الوحدة يجب أن يكون أكبر من صفر في البند {i+1}')
        
        # التحقق من الإجماليات
        if not invoice_data.get('grand_total') or invoice_data.get('grand_total') <= 0:
            errors.append('إجمالي الفاتورة يجب أن يكون أكبر من صفر')
        
        return {
            'is_valid': len(errors) == 0,
            'errors': errors
        }
    
    def submit_invoice_to_zatca(self, invoice_xml, certificate_data=None):
        """
        إرسال الفاتورة إلى منصة ZATCA
        """
        try:
            # تحضير البيانات للإرسال
            headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
            
            # إضافة شهادة التوقيع إذا كانت متوفرة
            if certificate_data:
                headers['Authorization'] = f'Bearer {certificate_data.get("access_token", "")}'
            
            # تحضير payload
            payload = {
                'invoiceXml': base64.b64encode(invoice_xml.encode()).decode(),
                'invoiceHash': self.generate_invoice_hash(invoice_xml)['hash'],
                'timestamp': datetime.now().isoformat()
            }
            
            # اختيار URL حسب البيئة
            api_url = self.config['compliance_api_url'] if self.config['environment'] == 'sandbox' else self.config['production_api_url']
            
            # إرسال الطلب
            response = requests.post(
                f"{api_url}/invoices",
                headers=headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                return {
                    'success': True,
                    'response': response.json(),
                    'invoice_uuid': response.json().get('invoiceUuid', '')
                }
            else:
                return {
                    'success': False,
                    'error': f'فشل في إرسال الفاتورة: {response.status_code} - {response.text}'
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f'خطأ في الاتصال مع ZATCA: {str(e)}'
            }
    
    def get_invoice_status(self, invoice_uuid, certificate_data=None):
        """
        الاستعلام عن حالة الفاتورة من ZATCA
        """
        try:
            headers = {
                'Accept': 'application/json'
            }
            
            if certificate_data:
                headers['Authorization'] = f'Bearer {certificate_data.get("access_token", "")}'
            
            api_url = self.config['compliance_api_url'] if self.config['environment'] == 'sandbox' else self.config['production_api_url']
            
            response = requests.get(
                f"{api_url}/invoices/{invoice_uuid}/status",
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                return {
                    'success': True,
                    'status': response.json()
                }
            else:
                return {
                    'success': False,
                    'error': f'فشل في الاستعلام عن حالة الفاتورة: {response.status_code}'
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f'خطأ في الاتصال مع ZATCA: {str(e)}'
            }
    
    def generate_compliance_report(self, start_date, end_date, invoices_data):
        """
        إنشاء تقرير الامتثال للمتطلبات
        """
        try:
            total_invoices = len(invoices_data)
            submitted_invoices = len([inv for inv in invoices_data if inv.get('zatca_status') == 'submitted'])
            approved_invoices = len([inv for inv in invoices_data if inv.get('zatca_status') == 'approved'])
            rejected_invoices = len([inv for inv in invoices_data if inv.get('zatca_status') == 'rejected'])
            
            total_amount = sum([inv.get('grand_total', 0) for inv in invoices_data])
            total_tax = sum([inv.get('tax_amount', 0) for inv in invoices_data])
            
            report = {
                'period': {
                    'start_date': start_date,
                    'end_date': end_date
                },
                'summary': {
                    'total_invoices': total_invoices,
                    'submitted_invoices': submitted_invoices,
                    'approved_invoices': approved_invoices,
                    'rejected_invoices': rejected_invoices,
                    'compliance_rate': (approved_invoices / total_invoices * 100) if total_invoices > 0 else 0
                },
                'financial': {
                    'total_amount': total_amount,
                    'total_tax': total_tax,
                    'currency': 'SAR'
                },
                'generated_at': datetime.now().isoformat()
            }
            
            return {
                'success': True,
                'report': report
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'فشل في إنشاء تقرير الامتثال: {str(e)}'
            }
