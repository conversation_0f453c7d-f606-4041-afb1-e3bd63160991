from flask import Blueprint, request, jsonify
from ..services.zatca_service import ZATCAService
from ..models.user import Invoice, InvoiceItem, Customer, Company, Product
import uuid
from datetime import datetime

zatca_bp = Blueprint('zatca', __name__)
zatca_service = ZATCAService()

@zatca_bp.route('/generate-qr', methods=['POST'])
def generate_qr_code():
    """
    إنشاء QR Code للفاتورة
    """
    try:
        data = request.get_json()
        invoice_id = data.get('invoice_id')
        
        if not invoice_id:
            return jsonify({'error': 'معرف الفاتورة مطلوب'}), 400
        
        # جلب بيانات الفاتورة
        invoice = Invoice.get_by_id(invoice_id)
        if not invoice:
            return jsonify({'error': 'الفاتورة غير موجودة'}), 404
        
        # تحضير بيانات الفاتورة
        invoice_data = prepare_invoice_data(invoice)
        
        # إنشاء QR Code
        result = zatca_service.generate_qr_code(invoice_data)
        
        if result['success']:
            # حفظ QR Code في الفاتورة
            invoice.qr_code = result['qr_code_data']
            invoice.qr_code_image = result['qr_code_image']
            invoice.save()
            
            return jsonify({
                'success': True,
                'qr_code': result['qr_code_data'],
                'qr_image': result['qr_code_image']
            })
        else:
            return jsonify({'error': result['error']}), 500
            
    except Exception as e:
        return jsonify({'error': f'خطأ في إنشاء QR Code: {str(e)}'}), 500

@zatca_bp.route('/generate-xml', methods=['POST'])
def generate_invoice_xml():
    """
    إنشاء XML للفاتورة
    """
    try:
        data = request.get_json()
        invoice_id = data.get('invoice_id')
        
        if not invoice_id:
            return jsonify({'error': 'معرف الفاتورة مطلوب'}), 400
        
        # جلب بيانات الفاتورة
        invoice = Invoice.get_by_id(invoice_id)
        if not invoice:
            return jsonify({'error': 'الفاتورة غير موجودة'}), 404
        
        # تحضير بيانات الفاتورة
        invoice_data = prepare_invoice_data(invoice)
        
        # إنشاء XML
        result = zatca_service.generate_invoice_xml(invoice_data)
        
        if result['success']:
            # حفظ XML في الفاتورة
            invoice.xml_content = result['xml']
            invoice.save()
            
            return jsonify({
                'success': True,
                'xml': result['xml']
            })
        else:
            return jsonify({'error': result['error']}), 500
            
    except Exception as e:
        return jsonify({'error': f'خطأ في إنشاء XML: {str(e)}'}), 500

@zatca_bp.route('/validate', methods=['POST'])
def validate_invoice():
    """
    التحقق من صحة الفاتورة
    """
    try:
        data = request.get_json()
        invoice_id = data.get('invoice_id')
        
        if not invoice_id:
            return jsonify({'error': 'معرف الفاتورة مطلوب'}), 400
        
        # جلب بيانات الفاتورة
        invoice = Invoice.get_by_id(invoice_id)
        if not invoice:
            return jsonify({'error': 'الفاتورة غير موجودة'}), 404
        
        # تحضير بيانات الفاتورة
        invoice_data = prepare_invoice_data(invoice)
        
        # التحقق من صحة البيانات
        validation_result = zatca_service.validate_invoice(invoice_data)
        
        return jsonify(validation_result)
        
    except Exception as e:
        return jsonify({'error': f'خطأ في التحقق من الفاتورة: {str(e)}'}), 500

@zatca_bp.route('/submit', methods=['POST'])
def submit_invoice():
    """
    إرسال الفاتورة إلى ZATCA
    """
    try:
        data = request.get_json()
        invoice_id = data.get('invoice_id')
        certificate_data = data.get('certificate_data')
        
        if not invoice_id:
            return jsonify({'error': 'معرف الفاتورة مطلوب'}), 400
        
        # جلب بيانات الفاتورة
        invoice = Invoice.get_by_id(invoice_id)
        if not invoice:
            return jsonify({'error': 'الفاتورة غير موجودة'}), 404
        
        # التحقق من وجود XML
        if not invoice.xml_content:
            # إنشاء XML إذا لم يكن موجوداً
            invoice_data = prepare_invoice_data(invoice)
            xml_result = zatca_service.generate_invoice_xml(invoice_data)
            
            if not xml_result['success']:
                return jsonify({'error': xml_result['error']}), 500
            
            invoice.xml_content = xml_result['xml']
            invoice.save()
        
        # إرسال الفاتورة
        result = zatca_service.submit_invoice_to_zatca(invoice.xml_content, certificate_data)
        
        if result['success']:
            # تحديث حالة الفاتورة
            invoice.zatca_status = 'submitted'
            invoice.zatca_uuid = result.get('invoice_uuid', '')
            invoice.zatca_submission_date = datetime.now()
            invoice.save()
            
            return jsonify({
                'success': True,
                'invoice_uuid': result.get('invoice_uuid', ''),
                'response': result.get('response', {})
            })
        else:
            return jsonify({'error': result['error']}), 500
            
    except Exception as e:
        return jsonify({'error': f'خطأ في إرسال الفاتورة: {str(e)}'}), 500

@zatca_bp.route('/status/<invoice_uuid>', methods=['GET'])
def get_invoice_status(invoice_uuid):
    """
    الاستعلام عن حالة الفاتورة
    """
    try:
        certificate_data = request.headers.get('Authorization')
        
        # الاستعلام عن الحالة
        result = zatca_service.get_invoice_status(invoice_uuid, certificate_data)
        
        if result['success']:
            # تحديث حالة الفاتورة في قاعدة البيانات
            invoice = Invoice.get_by_zatca_uuid(invoice_uuid)
            if invoice:
                status_data = result['status']
                invoice.zatca_status = status_data.get('status', 'unknown')
                invoice.save()
            
            return jsonify(result['status'])
        else:
            return jsonify({'error': result['error']}), 500
            
    except Exception as e:
        return jsonify({'error': f'خطأ في الاستعلام عن الحالة: {str(e)}'}), 500

@zatca_bp.route('/compliance-report', methods=['POST'])
def generate_compliance_report():
    """
    إنشاء تقرير الامتثال
    """
    try:
        data = request.get_json()
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        company_id = data.get('company_id')
        
        if not all([start_date, end_date, company_id]):
            return jsonify({'error': 'تاريخ البداية والنهاية ومعرف الشركة مطلوبة'}), 400
        
        # جلب الفواتير في الفترة المحددة
        invoices = Invoice.get_by_date_range(company_id, start_date, end_date)
        
        # تحضير بيانات الفواتير
        invoices_data = []
        for invoice in invoices:
            invoice_data = prepare_invoice_data(invoice)
            invoice_data['zatca_status'] = invoice.zatca_status
            invoices_data.append(invoice_data)
        
        # إنشاء التقرير
        result = zatca_service.generate_compliance_report(start_date, end_date, invoices_data)
        
        if result['success']:
            return jsonify(result['report'])
        else:
            return jsonify({'error': result['error']}), 500
            
    except Exception as e:
        return jsonify({'error': f'خطأ في إنشاء التقرير: {str(e)}'}), 500

@zatca_bp.route('/process-invoice', methods=['POST'])
def process_invoice_complete():
    """
    معالجة شاملة للفاتورة (QR + XML + التحقق)
    """
    try:
        data = request.get_json()
        invoice_id = data.get('invoice_id')
        
        if not invoice_id:
            return jsonify({'error': 'معرف الفاتورة مطلوب'}), 400
        
        # جلب بيانات الفاتورة
        invoice = Invoice.get_by_id(invoice_id)
        if not invoice:
            return jsonify({'error': 'الفاتورة غير موجودة'}), 404
        
        # تحضير بيانات الفاتورة
        invoice_data = prepare_invoice_data(invoice)
        
        # التحقق من صحة البيانات
        validation_result = zatca_service.validate_invoice(invoice_data)
        if not validation_result['is_valid']:
            return jsonify({
                'success': False,
                'errors': validation_result['errors']
            }), 400
        
        # إنشاء XML
        xml_result = zatca_service.generate_invoice_xml(invoice_data)
        if not xml_result['success']:
            return jsonify({'error': xml_result['error']}), 500
        
        # إنشاء QR Code
        qr_result = zatca_service.generate_qr_code(invoice_data)
        if not qr_result['success']:
            return jsonify({'error': qr_result['error']}), 500
        
        # حفظ البيانات
        invoice.xml_content = xml_result['xml']
        invoice.qr_code = qr_result['qr_code_data']
        invoice.qr_code_image = qr_result['qr_code_image']
        invoice.zatca_status = 'processed'
        invoice.save()
        
        return jsonify({
            'success': True,
            'xml': xml_result['xml'],
            'qr_code': qr_result['qr_code_data'],
            'qr_image': qr_result['qr_code_image'],
            'validation': validation_result
        })
        
    except Exception as e:
        return jsonify({'error': f'خطأ في معالجة الفاتورة: {str(e)}'}), 500

def prepare_invoice_data(invoice):
    """
    تحضير بيانات الفاتورة للمعالجة
    """
    # جلب بيانات الشركة والعميل
    company = Company.get_by_id(invoice.company_id)
    customer = Customer.get_by_id(invoice.customer_id)
    
    # جلب بنود الفاتورة
    items = InvoiceItem.get_by_invoice_id(invoice.invoice_id)
    
    # تحضير بيانات البنود
    items_data = []
    for i, item in enumerate(items):
        product = Product.get_by_id(item.product_id)
        items_data.append({
            'line_number': i + 1,
            'product_id': item.product_id,
            'product_name': product.name if product else 'منتج غير محدد',
            'quantity': item.quantity,
            'unit_price': item.unit_price,
            'total_amount': item.quantity * item.unit_price,
            'tax_rate': product.tax_rate if product else 15.0
        })
    
    return {
        'invoice_id': invoice.invoice_id,
        'invoice_number': invoice.invoice_number,
        'invoice_date': invoice.invoice_date.isoformat() if invoice.invoice_date else '',
        'due_date': invoice.due_date.isoformat() if invoice.due_date else '',
        'company_name': company.name if company else '',
        'company_tax_id': company.tax_id if company else '',
        'company_address': company.address if company else '',
        'customer_name': customer.name if customer else '',
        'customer_tax_id': customer.tax_id if customer else '',
        'customer_address': customer.address if customer else '',
        'items': items_data,
        'subtotal': invoice.subtotal or 0,
        'tax_amount': invoice.tax_amount or 0,
        'grand_total': invoice.grand_total or 0,
        'currency': 'SAR'
    }

