import React, { useState, useEffect } from 'react';
import { invoicesAPI, customersAPI, productsAPI } from '../services/api';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter } from '@/components/ui/dialog';
import { PencilIcon, TrashIcon, PlusIcon, EyeIcon } from '@heroicons/react/24/outline';
import { useAuth } from '../contexts/AuthContext';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

const InvoiceManagement = () => {
  const { user } = useAuth();
  const [invoices, setInvoices] = useState([]);
  const [customers, setCustomers] = useState([]);
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentInvoice, setCurrentInvoice] = useState(null);
  const [formData, setFormData] = useState({
    customer_id: '',
    invoice_number: '',
    invoice_date: new Date().toISOString().split('T')[0],
    due_date: '',
    status: 'draft',
    items: []
  });

  useEffect(() => {
    if (user && user.company_id) {
      fetchData(user.company_id);
    }
  }, [user]);

  const fetchData = async (companyId) => {
    try {
      setLoading(true);
      const [invoicesRes, customersRes, productsRes] = await Promise.all([
        invoicesAPI.getAll(companyId),
        customersAPI.getAll(companyId),
        productsAPI.getAll(companyId)
      ]);
      setInvoices(invoicesRes.data);
      setCustomers(customersRes.data);
      setProducts(productsRes.data);
    } catch (err) {
      setError('فشل في جلب البيانات.');
      console.error('Error fetching data:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { id, value } = e.target;
    setFormData(prev => ({ ...prev, [id]: value }));
  };

  const handleItemChange = (index, e) => {
    const { id, value } = e.target;
    const newItems = [...formData.items];
    newItems[index][id] = value;
    setFormData(prev => ({ ...prev, items: newItems }));
  };

  const handleAddItem = () => {
    setFormData(prev => ({
      ...prev,
      items: [...prev.items, { product_id: '', quantity: 1, unit_price: 0 }]
    }));
  };

  const handleRemoveItem = (index) => {
    const newItems = formData.items.filter((_, i) => i !== index);
    setFormData(prev => ({ ...prev, items: newItems }));
  };

  const handleAddEditInvoice = async (e) => {
    e.preventDefault();
    setError('');
    try {
      const dataToSend = {
        ...formData,
        company_id: user.company_id,
        items: formData.items.map(item => ({
          ...item,
          quantity: parseFloat(item.quantity),
          unit_price: parseFloat(item.unit_price)
        }))
      };

      if (currentInvoice) {
        await invoicesAPI.update(currentInvoice.invoice_id, dataToSend);
      } else {
        await invoicesAPI.create(dataToSend);
      }
      fetchData(user.company_id);
      setIsModalOpen(false);
      setFormData({
        customer_id: '',
        invoice_number: '',
        invoice_date: new Date().toISOString().split('T')[0],
        due_date: '',
        status: 'draft',
        items: []
      });
      setCurrentInvoice(null);
    } catch (err) {
      setError(err.response?.data?.error || 'فشل في حفظ بيانات الفاتورة.');
      console.error('Error saving invoice:', err);
    }
  };

  const handleDeleteInvoice = async (invoiceId) => {
    if (window.confirm('هل أنت متأكد أنك تريد حذف هذه الفاتورة؟')) {
      setError('');
      try {
        await invoicesAPI.delete(invoiceId);
        fetchData(user.company_id);
      } catch (err) {
        setError(err.response?.data?.error || 'فشل في حذف الفاتورة.');
        console.error('Error deleting invoice:', err);
      }
    }
  };

  const openEditModal = (invoice) => {
    setCurrentInvoice(invoice);
    setFormData({
      customer_id: invoice.customer_id,
      invoice_number: invoice.invoice_number,
      invoice_date: invoice.invoice_date,
      due_date: invoice.due_date || '',
      status: invoice.status,
      items: invoice.items.map(item => ({
        product_id: item.product_id,
        quantity: item.quantity,
        unit_price: item.unit_price
      }))
    });
    setIsModalOpen(true);
  };

  const openAddModal = () => {
    setCurrentInvoice(null);
    setFormData({
      customer_id: '',
      invoice_number: '',
      invoice_date: new Date().toISOString().split('T')[0],
      due_date: '',
      status: 'draft',
      items: []
    });
    setIsModalOpen(true);
  };

  if (loading) {
    return <div className="text-center py-8">جاري التحميل...</div>;
  }

  if (error) {
    return <div className="text-center py-8 text-red-500">خطأ: {error}</div>;
  }

  return (
    <div className="container mx-auto p-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-2xl font-bold">إدارة الفواتير</CardTitle>
          <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
            <DialogTrigger asChild>
              <Button onClick={openAddModal}>
                <PlusIcon className="h-5 w-5 ml-2" /> إنشاء فاتورة جديدة
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>{currentInvoice ? 'تعديل فاتورة' : 'إنشاء فاتورة جديدة'}</DialogTitle>
                <DialogDescription>
                  {currentInvoice ? 'قم بتعديل بيانات الفاتورة.' : 'قم بإنشاء فاتورة مبيعات جديدة.'}
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleAddEditInvoice} className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="customer_id" className="text-right">العميل</Label>
                  <Select
                    id="customer_id"
                    value={formData.customer_id}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, customer_id: value }))}
                    required
                  >
                    <SelectTrigger className="col-span-3 text-right">
                      <SelectValue placeholder="اختر عميل" />
                    </SelectTrigger>
                    <SelectContent>
                      {customers.map(customer => (
                        <SelectItem key={customer.customer_id} value={customer.customer_id}>
                          {customer.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="invoice_number" className="text-right">رقم الفاتورة</Label>
                  <Input id="invoice_number" value={formData.invoice_number} onChange={handleInputChange} className="col-span-3" required />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="invoice_date" className="text-right">تاريخ الفاتورة</Label>
                  <Input id="invoice_date" type="date" value={formData.invoice_date} onChange={handleInputChange} className="col-span-3" required />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="due_date" className="text-right">تاريخ الاستحقاق</Label>
                  <Input id="due_date" type="date" value={formData.due_date} onChange={handleInputChange} className="col-span-3" />
                </div>

                <h4 className="text-lg font-semibold mt-4">بنود الفاتورة</h4>
                {formData.items.map((item, index) => (
                  <div key={index} className="grid grid-cols-6 items-center gap-4 border-b pb-2 mb-2">
                    <Label htmlFor={`product_id-${index}`} className="text-right">المنتج</Label>
                    <Select
                      id={`product_id-${index}`}
                      value={item.product_id}
                      onValueChange={(value) => handleItemChange(index, { target: { id: 'product_id', value } })}
                      className="col-span-2"
                      required
                    >
                      <SelectTrigger className="text-right">
                        <SelectValue placeholder="اختر منتج" />
                      </SelectTrigger>
                      <SelectContent>
                        {products.map(product => (
                          <SelectItem key={product.product_id} value={product.product_id}>
                            {product.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <Label htmlFor={`quantity-${index}`} className="text-right">الكمية</Label>
                    <Input id={`quantity-${index}`} type="number" step="1" value={item.quantity} onChange={(e) => handleItemChange(index, e)} className="col-span-1" required />
                    <Label htmlFor={`unit_price-${index}`} className="text-right">سعر الوحدة</Label>
                    <Input id={`unit_price-${index}`} type="number" step="0.01" value={item.unit_price} onChange={(e) => handleItemChange(index, e)} className="col-span-1" required />
                    <Button type="button" variant="ghost" size="sm" onClick={() => handleRemoveItem(index)}>
                      <TrashIcon className="h-4 w-4 text-red-500" />
                    </Button>
                  </div>
                ))}
                <Button type="button" onClick={handleAddItem} variant="outline" className="w-full">
                  <PlusIcon className="h-4 w-4 ml-2" /> إضافة بند
                </Button>

                <DialogFooter>
                  <Button type="submit">{currentInvoice ? 'حفظ التعديلات' : 'إنشاء فاتورة'}</Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>رقم الفاتورة</TableHead>
                <TableHead>العميل</TableHead>
                <TableHead>التاريخ</TableHead>
                <TableHead>الإجمالي</TableHead>
                <TableHead>الحالة</TableHead>
                <TableHead className="text-right">الإجراءات</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {invoices.map((invoice) => (
                <TableRow key={invoice.invoice_id}>
                  <TableCell className="font-medium">{invoice.invoice_number}</TableCell>
                  <TableCell>{invoice.customer?.name || 'N/A'}</TableCell>
                  <TableCell>{invoice.invoice_date}</TableCell>
                  <TableCell>{invoice.grand_total} ر.س</TableCell>
                  <TableCell>{invoice.status}</TableCell>
                  <TableCell className="text-right">
                    <Button variant="ghost" size="sm" onClick={() => openEditModal(invoice)} className="ml-2">
                      <PencilIcon className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => handleDeleteInvoice(invoice.invoice_id)}>
                      <TrashIcon className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => console.log('View Invoice', invoice.invoice_id)}>
                      <EyeIcon className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default InvoiceManagement;

