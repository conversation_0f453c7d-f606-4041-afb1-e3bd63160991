from flask import Blueprint, jsonify, request
from src.models.user import User, db
from werkzeug.security import generate_password_hash, check_password_hash
import jwt
from datetime import datetime, timedelta
from functools import wraps

user_bp = Blueprint('user', __name__)

def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = request.headers.get('Authorization')
        if not token:
            return jsonify({'error': 'Token is missing'}), 401
        
        try:
            if token.startswith('Bearer '):
                token = token[7:]
            data = jwt.decode(token, 'asdf#FGSgvasgf$5$WGT', algorithms=['HS256'])
            current_user = User.query.get(data['user_id'])
            if not current_user:
                return jsonify({'error': 'Invalid token'}), 401
        except:
            return jsonify({'error': 'Token is invalid'}), 401
        
        return f(current_user, *args, **kwargs)
    return decorated

@user_bp.route('/auth/register', methods=['POST'])
def register():
    """Register a new user"""
    try:
        data = request.json
        
        # Validate required fields
        required_fields = ['username', 'email', 'password', 'company_id']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({'error': f'{field} is required'}), 400
        
        # Check if username or email already exists
        if User.query.filter_by(username=data['username']).first():
            return jsonify({'error': 'Username already exists'}), 400
        
        if User.query.filter_by(email=data['email']).first():
            return jsonify({'error': 'Email already exists'}), 400
        
        # Hash password
        password_hash = generate_password_hash(data['password'])
        
        user = User(
            company_id=data['company_id'],
            username=data['username'],
            email=data['email'],
            password_hash=password_hash,
            role=data.get('role', 'accountant')
        )
        
        db.session.add(user)
        db.session.commit()
        
        return jsonify(user.to_dict()), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@user_bp.route('/auth/login', methods=['POST'])
def login():
    """Login user and return JWT token"""
    try:
        data = request.json
        
        if not data.get('username') or not data.get('password'):
            return jsonify({'error': 'Username and password are required'}), 400
        
        user = User.query.filter_by(username=data['username']).first()
        
        if not user or not check_password_hash(user.password_hash, data['password']):
            return jsonify({'error': 'Invalid credentials'}), 401
        
        if not user.is_active:
            return jsonify({'error': 'Account is deactivated'}), 401
        
        # Generate JWT token
        token = jwt.encode({
            'user_id': user.user_id,
            'company_id': user.company_id,
            'role': user.role,
            'exp': datetime.utcnow() + timedelta(hours=24)
        }, 'asdf#FGSgvasgf$5$WGT', algorithm='HS256')
        
        return jsonify({
            'token': token,
            'user': user.to_dict()
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@user_bp.route('/users', methods=['GET'])
@token_required
def get_users(current_user):
    """Get all users for the current user's company"""
    users = User.query.filter_by(company_id=current_user.company_id).all()
    return jsonify([user.to_dict() for user in users])

@user_bp.route('/users', methods=['POST'])
@token_required
def create_user(current_user):
    """Create a new user (admin only)"""
    if current_user.role not in ['admin', 'manager']:
        return jsonify({'error': 'Insufficient permissions'}), 403
    
    try:
        data = request.json
        
        # Validate required fields
        required_fields = ['username', 'email', 'password']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({'error': f'{field} is required'}), 400
        
        # Check if username or email already exists
        if User.query.filter_by(username=data['username']).first():
            return jsonify({'error': 'Username already exists'}), 400
        
        if User.query.filter_by(email=data['email']).first():
            return jsonify({'error': 'Email already exists'}), 400
        
        # Hash password
        password_hash = generate_password_hash(data['password'])
        
        user = User(
            company_id=current_user.company_id,  # Same company as current user
            username=data['username'],
            email=data['email'],
            password_hash=password_hash,
            role=data.get('role', 'accountant')
        )
        
        db.session.add(user)
        db.session.commit()
        
        return jsonify(user.to_dict()), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@user_bp.route('/users/<user_id>', methods=['GET'])
@token_required
def get_user(current_user, user_id):
    """Get a specific user"""
    user = User.query.filter_by(user_id=user_id, company_id=current_user.company_id).first_or_404()
    return jsonify(user.to_dict())

@user_bp.route('/users/<user_id>', methods=['PUT'])
@token_required
def update_user(current_user, user_id):
    """Update a user"""
    # Users can update themselves, or admins can update anyone in their company
    if current_user.user_id != user_id and current_user.role not in ['admin', 'manager']:
        return jsonify({'error': 'Insufficient permissions'}), 403
    
    try:
        user = User.query.filter_by(user_id=user_id, company_id=current_user.company_id).first_or_404()
        data = request.json
        
        # Update fields if provided
        if 'username' in data:
            # Check if new username already exists (excluding current user)
            existing_user = User.query.filter(
                User.username == data['username'],
                User.user_id != user_id
            ).first()
            if existing_user:
                return jsonify({'error': 'Username already exists'}), 400
            user.username = data['username']
        
        if 'email' in data:
            # Check if new email already exists (excluding current user)
            existing_user = User.query.filter(
                User.email == data['email'],
                User.user_id != user_id
            ).first()
            if existing_user:
                return jsonify({'error': 'Email already exists'}), 400
            user.email = data['email']
        
        if 'password' in data:
            user.password_hash = generate_password_hash(data['password'])
        
        # Only admins can change roles and active status
        if current_user.role in ['admin', 'manager']:
            if 'role' in data:
                user.role = data['role']
            if 'is_active' in data:
                user.is_active = data['is_active']
        
        db.session.commit()
        return jsonify(user.to_dict())
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@user_bp.route('/users/<user_id>', methods=['DELETE'])
@token_required
def delete_user(current_user, user_id):
    """Delete a user (admin only)"""
    if current_user.role not in ['admin', 'manager']:
        return jsonify({'error': 'Insufficient permissions'}), 403
    
    if current_user.user_id == user_id:
        return jsonify({'error': 'Cannot delete yourself'}), 400
    
    try:
        user = User.query.filter_by(user_id=user_id, company_id=current_user.company_id).first_or_404()
        db.session.delete(user)
        db.session.commit()
        return '', 204
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@user_bp.route('/auth/me', methods=['GET'])
@token_required
def get_current_user(current_user):
    """Get current user info"""
    return jsonify(current_user.to_dict())
