#!/usr/bin/env python3
"""
تحسينات الأمان لنظام المحاسبة الإلكتروني
Security Enhancements for Accounting System
"""

import os
import hashlib
import secrets
import time
from datetime import datetime, timedelta
from functools import wraps
import sqlite3
import json
import re
import bleach  # مكتبة موصى بها لتنقية HTML
from typing import Dict, List, Optional

# تشفير البيانات الحساسة
class DataEncryption:
    """فئة تشفير البيانات الحساسة"""
    
    @staticmethod
    def generate_salt() -> str:
        """إنشاء salt عشوائي للتشفير"""
        return secrets.token_hex(32)
    
    @staticmethod
    def hash_sensitive_data(data: str, salt: str) -> str:
        """تشفير البيانات الحساسة"""
        return hashlib.pbkdf2_hmac('sha256', 
                                   data.encode('utf-8'), 
                                   salt.encode('utf-8'), 
                                   100000).hex()
    
    @staticmethod
    def encrypt_tax_id(tax_id: str) -> Dict[str, str]:
        """تشفير الرقم الضريبي"""
        salt = DataEncryption.generate_salt()
        encrypted = DataEncryption.hash_sensitive_data(tax_id, salt)
        return {
            'encrypted_tax_id': encrypted,
            'salt': salt
        }

# نظام الحماية من الهجمات
class SecurityProtection:
    """نظام الحماية من الهجمات المختلفة"""
    
    def __init__(self):
        self.failed_attempts = {}
        self.blocked_ips = {}
        self.rate_limits = {}
    
    def check_rate_limit(self, ip_address: str, endpoint: str, limit: int = 100) -> bool:
        """فحص حد المعدل للطلبات"""
        current_time = time.time()
        key = f"{ip_address}:{endpoint}"
        
        if key not in self.rate_limits:
            self.rate_limits[key] = []
        
        # إزالة الطلبات القديمة (أكثر من دقيقة)
        self.rate_limits[key] = [
            timestamp for timestamp in self.rate_limits[key]
            if current_time - timestamp < 60
        ]
        
        # فحص الحد
        if len(self.rate_limits[key]) >= limit:
            return False
        
        # إضافة الطلب الحالي
        self.rate_limits[key].append(current_time)
        return True
    
    def check_brute_force(self, ip_address: str, username: str) -> bool:
        """فحص محاولات القوة الغاشمة"""
        key = f"{ip_address}:{username}"
        current_time = time.time()
        
        if key not in self.failed_attempts:
            self.failed_attempts[key] = []
        
        # إزالة المحاولات القديمة (أكثر من 15 دقيقة)
        self.failed_attempts[key] = [
            timestamp for timestamp in self.failed_attempts[key]
            if current_time - timestamp < 900
        ]
        
        # فحص عدد المحاولات الفاشلة
        return len(self.failed_attempts[key]) < 5
    
    def record_failed_attempt(self, ip_address: str, username: str):
        """تسجيل محاولة فاشلة"""
        key = f"{ip_address}:{username}"
        if key not in self.failed_attempts:
            self.failed_attempts[key] = []
        self.failed_attempts[key].append(time.time())
    
    def is_ip_blocked(self, ip_address: str) -> bool:
        """فحص ما إذا كان IP محظور"""
        if ip_address in self.blocked_ips:
            block_time = self.blocked_ips[ip_address]
            if time.time() - block_time < 3600:  # حظر لمدة ساعة
                return True
            else:
                del self.blocked_ips[ip_address]
        return False
    
    def block_ip(self, ip_address: str):
        """حظر IP"""
        self.blocked_ips[ip_address] = time.time()

# التحقق من صحة البيانات
class DataValidation:
    """فئة التحقق من صحة البيانات"""
    
    @staticmethod
    def validate_tax_id(tax_id: str) -> bool:
        """التحقق من صحة الرقم الضريبي السعودي"""
        if not tax_id or len(tax_id) != 15:
            return False
        
        # يجب أن يبدأ بـ 3 وينتهي بـ 3
        if not (tax_id.startswith('3') and tax_id.endswith('3')):
            return False
        
        # يجب أن يحتوي على أرقام فقط
        if not tax_id.isdigit():
            return False
        
        return True
    
    @staticmethod
    def validate_email(email: str) -> bool:
        """التحقق من صحة البريد الإلكتروني"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    @staticmethod
    def validate_phone(phone: str) -> bool:
        """التحقق من صحة رقم الهاتف السعودي"""
        # إزالة المسافات والرموز
        clean_phone = re.sub(r'[^\d+]', '', phone)
        
        # التحقق من الأنماط المقبولة
        patterns = [
            r'^\+9665\d{8}$',  # +9665xxxxxxxx
            r'^9665\d{8}$',    # 9665xxxxxxxx
            r'^05\d{8}$',      # 05xxxxxxxx
            r'^5\d{8}$'        # 5xxxxxxxx
        ]
        
        return any(re.match(pattern, clean_phone) for pattern in patterns)
    
    @staticmethod
    def sanitize_input(input_str: str) -> str:
        """تنظيف المدخلات من الأكواد الضارة"""
        if not input_str:
            return ""
            
        # الطريقة الموصى بها: استخدام مكتبة متخصصة مثل bleach
        # تزيل هذه المكتبة أي وسوم (tags) وسمات (attributes) خطيرة
        # مع السماح بقائمة آمنة من الوسوم إذا لزم الأمر
        clean_str = bleach.clean(input_str, strip=True)
        
        # ملاحظة هامة حول SQL Injection:
        # أفضل طريقة لمنع SQL Injection هي استخدام Parameterized Queries
        # أو ORM مثل SQLAlchemy (الذي نستخدمه الآن).
        # محاولة إزالة كلمات SQL باستخدام regex غير فعالة وخطيرة.
        # لذا، تم حذف الجزء الخاص بـ SQL من هذه الدالة.
        
        return clean_str

# تدقيق الأمان
class SecurityAudit:
    """فئة تدقيق الأمان"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
    
    def log_security_event(self, event_type: str, user_id: int = None, 
                          ip_address: str = None, details: Dict = None):
        """تسجيل أحداث الأمان"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # إنشاء جدول أحداث الأمان إذا لم يكن موجوداً
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS security_events (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    event_type TEXT NOT NULL,
                    user_id INTEGER,
                    ip_address TEXT,
                    details TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    severity TEXT DEFAULT 'INFO'
                )
            ''')
            
            cursor.execute('''
                INSERT INTO security_events 
                (event_type, user_id, ip_address, details, severity)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                event_type,
                user_id,
                ip_address,
                json.dumps(details) if details else None,
                self._get_severity(event_type)
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"خطأ في تسجيل حدث الأمان: {e}")
    
    def _get_severity(self, event_type: str) -> str:
        """تحديد مستوى خطورة الحدث"""
        high_severity = [
            'FAILED_LOGIN_ATTEMPT',
            'BRUTE_FORCE_ATTACK',
            'SQL_INJECTION_ATTEMPT',
            'XSS_ATTEMPT',
            'UNAUTHORIZED_ACCESS',
            'DATA_BREACH_ATTEMPT'
        ]
        
        medium_severity = [
            'RATE_LIMIT_EXCEEDED',
            'INVALID_TOKEN',
            'PERMISSION_DENIED',
            'SUSPICIOUS_ACTIVITY'
        ]
        
        if event_type in high_severity:
            return 'HIGH'
        elif event_type in medium_severity:
            return 'MEDIUM'
        else:
            return 'LOW'
    
    def get_security_report(self, days: int = 7) -> Dict:
        """إنشاء تقرير أمان"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # إحصائيات الأحداث الأمنية
            cursor.execute('''
                SELECT event_type, severity, COUNT(*) as count
                FROM security_events 
                WHERE timestamp >= datetime('now', '-{} days')
                GROUP BY event_type, severity
                ORDER BY count DESC
            '''.format(days))
            
            events = cursor.fetchall()
            
            # أكثر IP عناوين نشاطاً
            cursor.execute('''
                SELECT ip_address, COUNT(*) as count
                FROM security_events 
                WHERE timestamp >= datetime('now', '-{} days')
                AND ip_address IS NOT NULL
                GROUP BY ip_address
                ORDER BY count DESC
                LIMIT 10
            '''.format(days))
            
            top_ips = cursor.fetchall()
            
            conn.close()
            
            return {
                'period_days': days,
                'events_summary': [
                    {
                        'event_type': event[0],
                        'severity': event[1],
                        'count': event[2]
                    }
                    for event in events
                ],
                'top_active_ips': [
                    {
                        'ip_address': ip[0],
                        'event_count': ip[1]
                    }
                    for ip in top_ips
                ]
            }
            
        except Exception as e:
            print(f"خطأ في إنشاء تقرير الأمان: {e}")
            return {}

# إعدادات الأمان المتقدمة
class AdvancedSecurity:
    """إعدادات الأمان المتقدمة"""
    
    @staticmethod
    def generate_secure_session_id() -> str:
        """إنشاء معرف جلسة آمن"""
        return secrets.token_urlsafe(32)
    
    @staticmethod
    def generate_csrf_token() -> str:
        """إنشاء رمز CSRF"""
        return secrets.token_urlsafe(32)
    
    @staticmethod
    def validate_csrf_token(token: str, session_token: str) -> bool:
        """التحقق من صحة رمز CSRF"""
        return secrets.compare_digest(token, session_token)
    
    @staticmethod
    def secure_headers() -> Dict[str, str]:
        """إعداد headers الأمان"""
        return {
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'X-XSS-Protection': '1; mode=block',
            'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
            'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'",
            'Referrer-Policy': 'strict-origin-when-cross-origin'
        }
    
    @staticmethod
    def check_password_strength(password: str) -> Dict[str, any]:
        """فحص قوة كلمة المرور"""
        score = 0
        feedback = []
        
        # الطول
        if len(password) >= 8:
            score += 1
        else:
            feedback.append("يجب أن تكون كلمة المرور 8 أحرف على الأقل")
        
        # الأحرف الكبيرة
        if re.search(r'[A-Z]', password):
            score += 1
        else:
            feedback.append("يجب أن تحتوي على حرف كبير واحد على الأقل")
        
        # الأحرف الصغيرة
        if re.search(r'[a-z]', password):
            score += 1
        else:
            feedback.append("يجب أن تحتوي على حرف صغير واحد على الأقل")
        
        # الأرقام
        if re.search(r'\d', password):
            score += 1
        else:
            feedback.append("يجب أن تحتوي على رقم واحد على الأقل")
        
        # الرموز الخاصة
        if re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            score += 1
        else:
            feedback.append("يجب أن تحتوي على رمز خاص واحد على الأقل")
        
        # تقييم القوة
        if score >= 5:
            strength = "قوية جداً"
        elif score >= 4:
            strength = "قوية"
        elif score >= 3:
            strength = "متوسطة"
        elif score >= 2:
            strength = "ضعيفة"
        else:
            strength = "ضعيفة جداً"
        
        return {
            'score': score,
            'max_score': 5,
            'strength': strength,
            'is_strong': score >= 4,
            'feedback': feedback
        }

# مثال على الاستخدام
if __name__ == "__main__":
    # اختبار تشفير البيانات
    tax_id = "300123456789003"
    encrypted_data = DataEncryption.encrypt_tax_id(tax_id)
    print(f"الرقم الضريبي المشفر: {encrypted_data}")
    
    # اختبار التحقق من البيانات
    validator = DataValidation()
    print(f"صحة الرقم الضريبي: {validator.validate_tax_id(tax_id)}")
    print(f"صحة البريد الإلكتروني: {validator.validate_email('<EMAIL>')}")
    print(f"صحة رقم الهاتف: {validator.validate_phone('+966501234567')}")
    
    # اختبار قوة كلمة المرور
    password_check = AdvancedSecurity.check_password_strength("MySecure123!")
    print(f"قوة كلمة المرور: {password_check}")
    
    print("تم تطبيق تحسينات الأمان بنجاح!")
