from flask import Flask, request, jsonify
from flask_cors import CORS
from datetime import datetime, timedelta
import json
import sqlite3
import os
import base64
import qrcode
from io import BytesIO

app = Flask(__name__)
CORS(app)

DATABASE = 'accounting.db'

def init_db():
    conn = sqlite3.connect(DATABASE)
    cursor = conn.cursor()
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS companies (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            tax_id TEXT UNIQUE,
            address TEXT,
            phone TEXT,
            email TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS customers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            company_id INTEGER,
            name TEXT NOT NULL,
            tax_id TEXT,
            address TEXT,
            phone TEXT,
            email TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (company_id) REFERENCES companies (id)
        )
    ''')
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS products (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            company_id INTEGER,
            name TEXT NOT NULL,
            description TEXT,
            price DECIMAL(10,2),
            tax_rate DECIMAL(5,2) DEFAULT 15.00,
            category TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (company_id) REFERENCES companies (id)
        )
    ''')
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS invoices (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            company_id INTEGER,
            customer_id INTEGER,
            invoice_number TEXT UNIQUE NOT NULL,
            invoice_date DATE,
            due_date DATE,
            subtotal DECIMAL(10,2),
            tax_amount DECIMAL(10,2),
            total_amount DECIMAL(10,2),
            status TEXT DEFAULT 'draft',
            qr_code TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (company_id) REFERENCES companies (id),
            FOREIGN KEY (customer_id) REFERENCES customers (id)
        )
    ''')
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS invoice_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            invoice_id INTEGER,
            product_id INTEGER,
            quantity DECIMAL(10,2),
            unit_price DECIMAL(10,2),
            tax_rate DECIMAL(5,2),
            line_total DECIMAL(10,2),
            FOREIGN KEY (invoice_id) REFERENCES invoices (id),
            FOREIGN KEY (product_id) REFERENCES products (id)
        )
    ''')
    
    conn.commit()
    conn.close()

def get_db_connection():
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    return conn

def generate_qr_code(invoice_data):
    try:
        seller_name = invoice_data.get('company_name', '')
        vat_number = invoice_data.get('company_tax_id', '')
        timestamp = invoice_data.get('invoice_date', '')
        total_amount = str(invoice_data.get('total_amount', 0))
        vat_amount = str(invoice_data.get('tax_amount', 0))
        
        def encode_tlv(tag, value):
            value_bytes = value.encode('utf-8')
            length = len(value_bytes)
            return bytes([tag, length]) + value_bytes
        
        qr_data = b''
        qr_data += encode_tlv(1, seller_name)
        qr_data += encode_tlv(2, vat_number)
        qr_data += encode_tlv(3, timestamp)
        qr_data += encode_tlv(4, total_amount)
        qr_data += encode_tlv(5, vat_amount)
        
        qr_string = base64.b64encode(qr_data).decode()
        
        qr = qrcode.QRCode(version=1, error_correction=qrcode.constants.ERROR_CORRECT_L, box_size=10, border=4)
        qr.add_data(qr_string)
        qr.make(fit=True)
        
        img = qr.make_image(fill_color="black", back_color="white")
        buffer = BytesIO()
        img.save(buffer, format='PNG')
        qr_image_base64 = base64.b64encode(buffer.getvalue()).decode()
        
        return {
            'success': True,
            'qr_code': qr_string,
            'qr_image': qr_image_base64
        }
    except Exception as e:
        return {'success': False, 'error': str(e)}

@app.route('/')
def home():
    return jsonify({
        'message': 'نظام المحاسبة الإلكتروني المتوافق مع ZATCA',
        'version': '1.0.0',
        'status': 'running',
        'features': [
            'إدارة الشركات والعملاء',
            'إدارة المنتجات والخدمات',
            'إنشاء فواتير متوافقة مع ZATCA',
            'تحليل المبيعات والتقارير',
            'QR Code للفواتير',
            'تتبع ضريبة القيمة المضافة'
        ]
    })

@app.route('/api/health')
def health_check():
    return jsonify({'status': 'healthy', 'timestamp': datetime.now().isoformat()})

@app.route('/api/companies', methods=['GET', 'POST'])
def companies():
    conn = get_db_connection()
    
    if request.method == 'POST':
        data = request.get_json()
        cursor = conn.cursor()
        cursor.execute('''
            INSERT INTO companies (name, tax_id, address, phone, email)
            VALUES (?, ?, ?, ?, ?)
        ''', (data['name'], data.get('tax_id'), data.get('address'), 
              data.get('phone'), data.get('email')))
        conn.commit()
        company_id = cursor.lastrowid
        conn.close()
        return jsonify({'id': company_id, 'message': 'تم إنشاء الشركة بنجاح'})
    
    companies = conn.execute('SELECT * FROM companies ORDER BY created_at DESC').fetchall()
    conn.close()
    return jsonify([dict(company) for company in companies])

@app.route('/api/customers', methods=['GET', 'POST'])
def customers():
    conn = get_db_connection()
    company_id = request.args.get('company_id', 1)
    
    if request.method == 'POST':
        data = request.get_json()
        cursor = conn.cursor()
        cursor.execute('''
            INSERT INTO customers (company_id, name, tax_id, address, phone, email)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (company_id, data['name'], data.get('tax_id'), data.get('address'),
              data.get('phone'), data.get('email')))
        conn.commit()
        customer_id = cursor.lastrowid
        conn.close()
        return jsonify({'id': customer_id, 'message': 'تم إنشاء العميل بنجاح'})
    
    customers = conn.execute('''
        SELECT * FROM customers WHERE company_id = ? ORDER BY created_at DESC
    ''', (company_id,)).fetchall()
    conn.close()
    return jsonify([dict(customer) for customer in customers])

@app.route('/api/products', methods=['GET', 'POST'])
def products():
    conn = get_db_connection()
    company_id = request.args.get('company_id', 1)
    
    if request.method == 'POST':
        data = request.get_json()
        cursor = conn.cursor()
        cursor.execute('''
            INSERT INTO products (company_id, name, description, price, tax_rate, category)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (company_id, data['name'], data.get('description'), data['price'],
              data.get('tax_rate', 15.0), data.get('category')))
        conn.commit()
        product_id = cursor.lastrowid
        conn.close()
        return jsonify({'id': product_id, 'message': 'تم إنشاء المنتج بنجاح'})
    
    products = conn.execute('''
        SELECT * FROM products WHERE company_id = ? ORDER BY created_at DESC
    ''', (company_id,)).fetchall()
    conn.close()
    return jsonify([dict(product) for product in products])

@app.route('/api/invoices', methods=['GET', 'POST'])
def invoices():
    conn = get_db_connection()
    company_id = request.args.get('company_id', 1)
    
    if request.method == 'POST':
        data = request.get_json()
        
        subtotal = 0
        tax_amount = 0
        
        for item in data.get('items', []):
            line_total = item['quantity'] * item['unit_price']
            line_tax = line_total * (item.get('tax_rate', 15.0) / 100)
            subtotal += line_total
            tax_amount += line_tax
        
        total_amount = subtotal + tax_amount
        
        cursor = conn.cursor()
        cursor.execute('''
            INSERT INTO invoices (company_id, customer_id, invoice_number, invoice_date, 
                                due_date, subtotal, tax_amount, total_amount, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (company_id, data['customer_id'], data['invoice_number'], 
              data['invoice_date'], data.get('due_date'), subtotal, tax_amount, 
              total_amount, data.get('status', 'draft')))
        
        invoice_id = cursor.lastrowid
        
        for item in data.get('items', []):
            line_total = item['quantity'] * item['unit_price']
            cursor.execute('''
                INSERT INTO invoice_items (invoice_id, product_id, quantity, unit_price, tax_rate, line_total)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (invoice_id, item['product_id'], item['quantity'], item['unit_price'],
                  item.get('tax_rate', 15.0), line_total))
        
        company = conn.execute('SELECT * FROM companies WHERE id = ?', (company_id,)).fetchone()
        qr_data = {
            'company_name': company['name'] if company else '',
            'company_tax_id': company['tax_id'] if company else '',
            'invoice_date': data['invoice_date'],
            'total_amount': total_amount,
            'tax_amount': tax_amount
        }
        
        qr_result = generate_qr_code(qr_data)
        if qr_result['success']:
            cursor.execute('UPDATE invoices SET qr_code = ? WHERE id = ?', 
                          (qr_result['qr_code'], invoice_id))
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'id': invoice_id,
            'message': 'تم إنشاء الفاتورة بنجاح',
            'total_amount': total_amount,
            'qr_code': qr_result.get('qr_code', ''),
            'qr_image': qr_result.get('qr_image', '')
        })
    
    invoices = conn.execute('''
        SELECT i.*, c.name as customer_name, comp.name as company_name
        FROM invoices i
        LEFT JOIN customers c ON i.customer_id = c.id
        LEFT JOIN companies comp ON i.company_id = comp.id
        WHERE i.company_id = ?
        ORDER BY i.created_at DESC
    ''', (company_id,)).fetchall()
    conn.close()
    return jsonify([dict(invoice) for invoice in invoices])

@app.route('/api/analytics/sales')
def sales_analytics():
    conn = get_db_connection()
    company_id = request.args.get('company_id', 1)
    start_date = request.args.get('start_date', (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'))
    end_date = request.args.get('end_date', datetime.now().strftime('%Y-%m-%d'))
    
    total_sales = conn.execute('''
        SELECT COALESCE(SUM(total_amount), 0) as total
        FROM invoices 
        WHERE company_id = ? AND invoice_date BETWEEN ? AND ? AND status != 'cancelled'
    ''', (company_id, start_date, end_date)).fetchone()
    
    total_tax = conn.execute('''
        SELECT COALESCE(SUM(tax_amount), 0) as total
        FROM invoices 
        WHERE company_id = ? AND invoice_date BETWEEN ? AND ? AND status != 'cancelled'
    ''', (company_id, start_date, end_date)).fetchone()
    
    invoice_count = conn.execute('''
        SELECT COUNT(*) as count
        FROM invoices 
        WHERE company_id = ? AND invoice_date BETWEEN ? AND ? AND status != 'cancelled'
    ''', (company_id, start_date, end_date)).fetchone()
    
    top_products = conn.execute('''
        SELECT p.name, SUM(ii.quantity) as total_quantity, SUM(ii.line_total) as total_sales
        FROM invoice_items ii
        JOIN invoices i ON ii.invoice_id = i.id
        JOIN products p ON ii.product_id = p.id
        WHERE i.company_id = ? AND i.invoice_date BETWEEN ? AND ? AND i.status != 'cancelled'
        GROUP BY p.id, p.name
        ORDER BY total_sales DESC
        LIMIT 10
    ''', (company_id, start_date, end_date)).fetchall()
    
    conn.close()
    
    return jsonify({
        'period': {'start_date': start_date, 'end_date': end_date},
        'summary': {
            'total_sales': float(total_sales['total']),
            'total_tax': float(total_tax['total']),
            'invoice_count': invoice_count['count'],
            'average_invoice': float(total_sales['total']) / max(invoice_count['count'], 1)
        },
        'top_products': [dict(product) for product in top_products]
    })

@app.route('/api/seed-data', methods=['POST'])
def seed_data():
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            INSERT OR IGNORE INTO companies (name, tax_id, address, phone, email)
            VALUES (?, ?, ?, ?, ?)
        ''', ('شركة التقنية المتقدمة', '300123456789003', 'الرياض، المملكة العربية السعودية', 
              '+966112345678', '<EMAIL>'))
        
        company_id = cursor.lastrowid or 1
        
        customers_data = [
            ('عميل تجريبي 1', '310123456789001', 'جدة، المملكة العربية السعودية', '+966123456789', '<EMAIL>'),
            ('عميل تجريبي 2', '310123456789002', 'الدمام، المملكة العربية السعودية', '+966123456790', '<EMAIL>')
        ]
        
        for customer in customers_data:
            cursor.execute('''
                INSERT OR IGNORE INTO customers (company_id, name, tax_id, address, phone, email)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (company_id,) + customer)
        
        products_data = [
            ('خدمة استشارية', 'خدمة استشارية في مجال التقنية', 1000.00, 15.0, 'خدمات'),
            ('برنامج محاسبة', 'برنامج محاسبة متكامل', 5000.00, 15.0, 'برمجيات'),
            ('دعم فني', 'دعم فني شهري', 500.00, 15.0, 'خدمات')
        ]
        
        for product in products_data:
            cursor.execute('''
                INSERT OR IGNORE INTO products (company_id, name, description, price, tax_rate, category)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (company_id,) + product)
        
        conn.commit()
        conn.close()
        
        return jsonify({'message': 'تم إضافة البيانات التجريبية بنجاح'})
        
    except Exception as e:
        conn.rollback()
        conn.close()
        return jsonify({'error': f'خطأ في إضافة البيانات: {str(e)}'}), 500

if __name__ == '__main__':
    init_db()
    app.run(host='0.0.0.0', port=5000, debug=True)

