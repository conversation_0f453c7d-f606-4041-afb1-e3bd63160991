from flask import Blueprint, jsonify, request
from src.models.user import Company, db

company_bp = Blueprint('company', __name__)

@company_bp.route('/companies', methods=['GET'])
def get_companies():
    """Get all companies"""
    companies = Company.query.all()
    return jsonify([company.to_dict() for company in companies])

@company_bp.route('/companies', methods=['POST'])
def create_company():
    """Create a new company"""
    try:
        data = request.json
        
        # Validate required fields
        required_fields = ['name', 'tax_id']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({'error': f'{field} is required'}), 400
        
        # Check if tax_id already exists
        existing_company = Company.query.filter_by(tax_id=data['tax_id']).first()
        if existing_company:
            return jsonify({'error': 'Company with this tax ID already exists'}), 400
        
        company = Company(
            name=data['name'],
            tax_id=data['tax_id'],
            address=data.get('address'),
            contact_person=data.get('contact_person'),
            email=data.get('email'),
            phone=data.get('phone')
        )
        
        db.session.add(company)
        db.session.commit()
        
        return jsonify(company.to_dict()), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@company_bp.route('/companies/<company_id>', methods=['GET'])
def get_company(company_id):
    """Get a specific company by ID"""
    company = Company.query.get_or_404(company_id)
    return jsonify(company.to_dict())

@company_bp.route('/companies/<company_id>', methods=['PUT'])
def update_company(company_id):
    """Update a company"""
    try:
        company = Company.query.get_or_404(company_id)
        data = request.json
        
        # Update fields if provided
        if 'name' in data:
            company.name = data['name']
        if 'tax_id' in data:
            # Check if new tax_id already exists (excluding current company)
            existing_company = Company.query.filter(
                Company.tax_id == data['tax_id'],
                Company.company_id != company_id
            ).first()
            if existing_company:
                return jsonify({'error': 'Company with this tax ID already exists'}), 400
            company.tax_id = data['tax_id']
        if 'address' in data:
            company.address = data['address']
        if 'contact_person' in data:
            company.contact_person = data['contact_person']
        if 'email' in data:
            company.email = data['email']
        if 'phone' in data:
            company.phone = data['phone']
        
        db.session.commit()
        return jsonify(company.to_dict())
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@company_bp.route('/companies/<company_id>', methods=['DELETE'])
def delete_company(company_id):
    """Delete a company"""
    try:
        company = Company.query.get_or_404(company_id)
        db.session.delete(company)
        db.session.commit()
        return '', 204
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

