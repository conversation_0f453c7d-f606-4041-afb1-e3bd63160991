#!/usr/bin/env python3
"""
نظام المحاسبة الإلكتروني المتوافق مع ZATCA - نسخة الإنتاج
Production-Ready Accounting System Backend
"""

import os
import sys
import logging
import sqlite3
import hashlib
import hmac
import time
from datetime import datetime, timedelta
from functools import wraps
from typing import Dict, List, Optional, Any
import uuid
import base64
import qrcode
from io import BytesIO
import json

from flask import Flask, request, jsonify, g, send_file
from flask_cors import CORS
from werkzeug.security import generate_password_hash, check_password_hash
import jwt

# إعداد التسجيل المتقدم
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('accounting_system.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# إعدادات التطبيق
class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-super-secret-key-change-in-production'
    DATABASE_URL = os.environ.get('DATABASE_URL') or 'accounting_production.db'
    JWT_EXPIRATION_DELTA = timedelta(hours=24)
    RATE_LIMIT_PER_MINUTE = 100
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size
    
    # إعدادات ZATCA
    ZATCA_ENVIRONMENT = os.environ.get('ZATCA_ENVIRONMENT', 'sandbox')
    COMPANY_TAX_ID = os.environ.get('COMPANY_TAX_ID', '***************')
    COMPANY_NAME = os.environ.get('COMPANY_NAME', 'شركة التقنية المتقدمة')

app = Flask(__name__)
app.config.from_object(Config)
CORS(app, origins="*", allow_headers=["Content-Type", "Authorization"])

# إعداد قاعدة البيانات المحسنة
def get_db():
    """الحصول على اتصال قاعدة البيانات مع تحسينات الأداء"""
    if 'db' not in g:
        g.db = sqlite3.connect(
            app.config['DATABASE_URL'],
            detect_types=sqlite3.PARSE_DECLTYPES | sqlite3.PARSE_COLNAMES,
            check_same_thread=False
        )
        g.db.row_factory = sqlite3.Row
        
        # تحسينات الأداء
        g.db.execute('PRAGMA journal_mode=WAL')
        g.db.execute('PRAGMA synchronous=NORMAL')
        g.db.execute('PRAGMA cache_size=10000')
        g.db.execute('PRAGMA temp_store=MEMORY')
        
    return g.db

def close_db(e=None):
    """إغلاق اتصال قاعدة البيانات"""
    db = g.pop('db', None)
    if db is not None:
        db.close()

def init_db():
    """تهيئة قاعدة البيانات مع الفهارس المحسنة"""
    db = get_db()
    
    # إنشاء الجداول
    db.executescript('''
        -- جدول الشركات
        CREATE TABLE IF NOT EXISTS companies (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            tax_id TEXT UNIQUE NOT NULL,
            address TEXT NOT NULL,
            phone TEXT,
            email TEXT,
            logo_path TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        -- جدول المستخدمين مع أدوار محسنة
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            email TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            role TEXT DEFAULT 'user' CHECK(role IN ('admin', 'manager', 'user', 'viewer')),
            company_id INTEGER,
            is_active BOOLEAN DEFAULT 1,
            last_login TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (company_id) REFERENCES companies (id)
        );
        
        -- جدول العملاء
        CREATE TABLE IF NOT EXISTS customers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            tax_id TEXT,
            address TEXT,
            phone TEXT,
            email TEXT,
            customer_type TEXT DEFAULT 'individual' CHECK(customer_type IN ('individual', 'business')),
            credit_limit DECIMAL(15,2) DEFAULT 0,
            company_id INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (company_id) REFERENCES companies (id)
        );
        
        -- جدول المنتجات والخدمات
        CREATE TABLE IF NOT EXISTS products (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            description TEXT,
            sku TEXT UNIQUE,
            price DECIMAL(10,2) NOT NULL,
            cost DECIMAL(10,2) DEFAULT 0,
            tax_rate DECIMAL(5,2) DEFAULT 15.0,
            category TEXT,
            unit TEXT DEFAULT 'قطعة',
            stock_quantity INTEGER DEFAULT 0,
            min_stock_level INTEGER DEFAULT 0,
            is_service BOOLEAN DEFAULT 0,
            is_active BOOLEAN DEFAULT 1,
            company_id INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (company_id) REFERENCES companies (id)
        );
        
        -- جدول الفواتير المحسن
        CREATE TABLE IF NOT EXISTS invoices (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            invoice_number TEXT UNIQUE NOT NULL,
            customer_id INTEGER NOT NULL,
            company_id INTEGER NOT NULL,
            invoice_date DATE NOT NULL,
            due_date DATE,
            subtotal DECIMAL(15,2) NOT NULL,
            tax_amount DECIMAL(15,2) NOT NULL,
            discount_amount DECIMAL(15,2) DEFAULT 0,
            total_amount DECIMAL(15,2) NOT NULL,
            status TEXT DEFAULT 'draft' CHECK(status IN ('draft', 'sent', 'paid', 'overdue', 'cancelled')),
            payment_method TEXT,
            payment_date DATE,
            notes TEXT,
            qr_code TEXT,
            zatca_uuid TEXT,
            zatca_hash TEXT,
            created_by INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (customer_id) REFERENCES customers (id),
            FOREIGN KEY (company_id) REFERENCES companies (id),
            FOREIGN KEY (created_by) REFERENCES users (id)
        );
        
        -- جدول عناصر الفواتير
        CREATE TABLE IF NOT EXISTS invoice_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            invoice_id INTEGER NOT NULL,
            product_id INTEGER,
            description TEXT NOT NULL,
            quantity DECIMAL(10,3) NOT NULL,
            unit_price DECIMAL(10,2) NOT NULL,
            discount_percentage DECIMAL(5,2) DEFAULT 0,
            tax_rate DECIMAL(5,2) NOT NULL,
            line_total DECIMAL(15,2) NOT NULL,
            FOREIGN KEY (invoice_id) REFERENCES invoices (id) ON DELETE CASCADE,
            FOREIGN KEY (product_id) REFERENCES products (id)
        );
        
        -- جدول المدفوعات
        CREATE TABLE IF NOT EXISTS payments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            invoice_id INTEGER NOT NULL,
            amount DECIMAL(15,2) NOT NULL,
            payment_method TEXT NOT NULL,
            payment_date DATE NOT NULL,
            reference_number TEXT,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (invoice_id) REFERENCES invoices (id)
        );
        
        -- جدول سجل النشاطات
        CREATE TABLE IF NOT EXISTS activity_log (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            action TEXT NOT NULL,
            table_name TEXT,
            record_id INTEGER,
            old_values TEXT,
            new_values TEXT,
            ip_address TEXT,
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        );
        
        -- إنشاء الفهارس لتحسين الأداء
        CREATE INDEX IF NOT EXISTS idx_invoices_date ON invoices(invoice_date);
        CREATE INDEX IF NOT EXISTS idx_invoices_customer ON invoices(customer_id);
        CREATE INDEX IF NOT EXISTS idx_invoices_status ON invoices(status);
        CREATE INDEX IF NOT EXISTS idx_invoices_company ON invoices(company_id);
        CREATE INDEX IF NOT EXISTS idx_customers_company ON customers(company_id);
        CREATE INDEX IF NOT EXISTS idx_products_company ON products(company_id);
        CREATE INDEX IF NOT EXISTS idx_products_sku ON products(sku);
        CREATE INDEX IF NOT EXISTS idx_users_company ON users(company_id);
        CREATE INDEX IF NOT EXISTS idx_activity_log_user ON activity_log(user_id);
        CREATE INDEX IF NOT EXISTS idx_activity_log_date ON activity_log(created_at);
    ''')
    
    db.commit()
    logger.info("تم تهيئة قاعدة البيانات بنجاح")

# نظام المصادقة المحسن
def token_required(f):
    """مصادقة JWT محسنة مع تسجيل النشاطات"""
    @wraps(f)
    def decorated(*args, **kwargs):
        token = request.headers.get('Authorization')
        
        if not token:
            return jsonify({'message': 'مطلوب رمز المصادقة'}), 401
            
        try:
            if token.startswith('Bearer '):
                token = token[7:]
                
            data = jwt.decode(token, app.config['SECRET_KEY'], algorithms=['HS256'])
            current_user_id = data['user_id']
            
            # التحقق من صحة المستخدم
            db = get_db()
            user = db.execute(
                'SELECT * FROM users WHERE id = ? AND is_active = 1',
                (current_user_id,)
            ).fetchone()
            
            if not user:
                return jsonify({'message': 'مستخدم غير صالح'}), 401
                
            g.current_user = dict(user)
            
        except jwt.ExpiredSignatureError:
            return jsonify({'message': 'انتهت صلاحية رمز المصادقة'}), 401
        except jwt.InvalidTokenError:
            return jsonify({'message': 'رمز مصادقة غير صالح'}), 401
            
        return f(*args, **kwargs)
    return decorated

def log_activity(action: str, table_name: str = None, record_id: int = None, 
                old_values: dict = None, new_values: dict = None):
    """تسجيل النشاطات"""
    try:
        db = get_db()
        user_id = g.get('current_user', {}).get('id')
        ip_address = request.remote_addr
        user_agent = request.headers.get('User-Agent', '')
        
        db.execute('''
            INSERT INTO activity_log 
            (user_id, action, table_name, record_id, old_values, new_values, ip_address, user_agent)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            user_id, action, table_name, record_id,
            json.dumps(old_values) if old_values else None,
            json.dumps(new_values) if new_values else None,
            ip_address, user_agent
        ))
        db.commit()
    except Exception as e:
        logger.error(f"خطأ في تسجيل النشاط: {e}")

# نظام ZATCA المحسن
class ZATCAService:
    """خدمة ZATCA محسنة للفواتير الإلكترونية"""
    
    @staticmethod
    def generate_tlv_qr(invoice_data: Dict) -> str:
        """إنشاء QR Code بتقنية TLV محسنة"""
        try:
            # البيانات المطلوبة وفقاً لمعايير ZATCA
            seller_name = invoice_data.get('seller_name', '')
            tax_number = invoice_data.get('tax_number', '')
            timestamp = invoice_data.get('timestamp', datetime.now().isoformat())
            total_amount = f"{float(invoice_data.get('total_amount', 0)):.2f}"
            tax_amount = f"{float(invoice_data.get('tax_amount', 0)):.2f}"
            
            # تكوين TLV
            tlv_data = b''
            
            # Tag 1: اسم البائع
            seller_name_bytes = seller_name.encode('utf-8')
            tlv_data += bytes([1]) + bytes([len(seller_name_bytes)]) + seller_name_bytes
            
            # Tag 2: الرقم الضريبي
            tax_number_bytes = tax_number.encode('utf-8')
            tlv_data += bytes([2]) + bytes([len(tax_number_bytes)]) + tax_number_bytes
            
            # Tag 3: الطابع الزمني
            timestamp_bytes = timestamp.encode('utf-8')
            tlv_data += bytes([3]) + bytes([len(timestamp_bytes)]) + timestamp_bytes
            
            # Tag 4: إجمالي المبلغ
            total_amount_bytes = total_amount.encode('utf-8')
            tlv_data += bytes([4]) + bytes([len(total_amount_bytes)]) + total_amount_bytes
            
            # Tag 5: ضريبة القيمة المضافة
            tax_amount_bytes = tax_amount.encode('utf-8')
            tlv_data += bytes([5]) + bytes([len(tax_amount_bytes)]) + tax_amount_bytes
            
            # تشفير Base64
            qr_string = base64.b64encode(tlv_data).decode('utf-8')
            
            logger.info(f"تم إنشاء QR Code للفاتورة: {invoice_data.get('invoice_number')}")
            return qr_string
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء QR Code: {e}")
            return ""
    
    @staticmethod
    def generate_qr_image(qr_string: str) -> str:
        """إنشاء صورة QR Code"""
        try:
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(qr_string)
            qr.make(fit=True)
            
            img = qr.make_image(fill_color="black", back_color="white")
            
            # تحويل إلى Base64
            buffer = BytesIO()
            img.save(buffer, format='PNG')
            img_str = base64.b64encode(buffer.getvalue()).decode()
            
            return img_str
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء صورة QR: {e}")
            return ""

# مسارات API محسنة

@app.route('/api/health', methods=['GET'])
def health_check():
    """فحص صحة النظام"""
    try:
        db = get_db()
        db.execute('SELECT 1').fetchone()
        
        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'version': '2.0.0',
            'database': 'connected',
            'features': [
                'إدارة الشركات والعملاء المحسنة',
                'إدارة المنتجات مع المخزون',
                'فواتير متوافقة مع ZATCA v2.0',
                'تحليل المبيعات المتقدم',
                'نظام أمان محسن',
                'تسجيل النشاطات',
                'نسخ احتياطي تلقائي'
            ]
        })
    except Exception as e:
        logger.error(f"خطأ في فحص الصحة: {e}")
        return jsonify({'status': 'unhealthy', 'error': str(e)}), 500

@app.route('/api/auth/login', methods=['POST'])
def login():
    """تسجيل الدخول المحسن"""
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')
        
        if not username or not password:
            return jsonify({'message': 'اسم المستخدم وكلمة المرور مطلوبان'}), 400
        
        db = get_db()
        user = db.execute(
            'SELECT * FROM users WHERE username = ? AND is_active = 1',
            (username,)
        ).fetchone()
        
        if user and check_password_hash(user['password_hash'], password):
            # تحديث آخر تسجيل دخول
            db.execute(
                'UPDATE users SET last_login = ? WHERE id = ?',
                (datetime.now(), user['id'])
            )
            db.commit()
            
            # إنشاء JWT Token
            token = jwt.encode({
                'user_id': user['id'],
                'username': user['username'],
                'role': user['role'],
                'exp': datetime.utcnow() + app.config['JWT_EXPIRATION_DELTA']
            }, app.config['SECRET_KEY'], algorithm='HS256')
            
            log_activity('تسجيل دخول', 'users', user['id'])
            
            return jsonify({
                'token': token,
                'user': {
                    'id': user['id'],
                    'username': user['username'],
                    'email': user['email'],
                    'role': user['role'],
                    'company_id': user['company_id']
                },
                'message': 'تم تسجيل الدخول بنجاح'
            })
        else:
            log_activity('محاولة تسجيل دخول فاشلة', 'users', None, {'username': username})
            return jsonify({'message': 'اسم المستخدم أو كلمة المرور غير صحيحة'}), 401
            
    except Exception as e:
        logger.error(f"خطأ في تسجيل الدخول: {e}")
        return jsonify({'message': 'خطأ في الخادم'}), 500

@app.route('/api/companies', methods=['GET'])
def get_companies():
    """جلب الشركات"""
    try:
        db = get_db()
        companies = db.execute('''
            SELECT id, name, tax_id, address, phone, email, 
                   datetime(created_at, 'localtime') as created_at
            FROM companies 
            ORDER BY created_at DESC
        ''').fetchall()
        
        return jsonify([dict(company) for company in companies])
        
    except Exception as e:
        logger.error(f"خطأ في جلب الشركات: {e}")
        return jsonify({'message': 'خطأ في جلب الشركات'}), 500

@app.route('/api/customers', methods=['GET'])
def get_customers():
    """جلب العملاء"""
    try:
        db = get_db()
        customers = db.execute('''
            SELECT id, name, tax_id, address, phone, email, customer_type,
                   datetime(created_at, 'localtime') as created_at
            FROM customers 
            ORDER BY created_at DESC
        ''').fetchall()
        
        return jsonify([dict(customer) for customer in customers])
        
    except Exception as e:
        logger.error(f"خطأ في جلب العملاء: {e}")
        return jsonify({'message': 'خطأ في جلب العملاء'}), 500

@app.route('/api/products', methods=['GET'])
def get_products():
    """جلب المنتجات"""
    try:
        db = get_db()
        products = db.execute('''
            SELECT id, name, description, sku, price, tax_rate, category, unit,
                   stock_quantity, is_service, is_active,
                   datetime(created_at, 'localtime') as created_at
            FROM products 
            WHERE is_active = 1
            ORDER BY created_at DESC
        ''').fetchall()
        
        return jsonify([dict(product) for product in products])
        
    except Exception as e:
        logger.error(f"خطأ في جلب المنتجات: {e}")
        return jsonify({'message': 'خطأ في جلب المنتجات'}), 500

@app.route('/api/invoices', methods=['GET'])
def get_invoices():
    """جلب الفواتير"""
    try:
        db = get_db()
        invoices = db.execute('''
            SELECT i.id, i.invoice_number, i.invoice_date, i.due_date,
                   i.subtotal, i.tax_amount, i.total_amount, i.status,
                   c.name as customer_name,
                   datetime(i.created_at, 'localtime') as created_at
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            ORDER BY i.created_at DESC
            LIMIT 50
        ''').fetchall()
        
        return jsonify([dict(invoice) for invoice in invoices])
        
    except Exception as e:
        logger.error(f"خطأ في جلب الفواتير: {e}")
        return jsonify({'message': 'خطأ في جلب الفواتير'}), 500

@app.route('/api/invoices', methods=['POST'])
def create_invoice():
    """إنشاء فاتورة محسنة مع ZATCA"""
    try:
        data = request.get_json()
        
        # التحقق من البيانات المطلوبة
        required_fields = ['customer_id', 'items']
        for field in required_fields:
            if field not in data:
                return jsonify({'message': f'الحقل {field} مطلوب'}), 400
        
        db = get_db()
        
        # التحقق من وجود العميل
        customer = db.execute(
            'SELECT * FROM customers WHERE id = ?',
            (data['customer_id'],)
        ).fetchone()
        
        if not customer:
            return jsonify({'message': 'العميل غير موجود'}), 404
        
        # حساب المبالغ
        subtotal = 0
        tax_amount = 0
        
        for item in data['items']:
            quantity = float(item.get('quantity', 1))
            unit_price = float(item.get('unit_price', 0))
            tax_rate = float(item.get('tax_rate', 15.0))
            discount = float(item.get('discount_percentage', 0))
            
            line_subtotal = quantity * unit_price
            line_discount = line_subtotal * (discount / 100)
            line_net = line_subtotal - line_discount
            line_tax = line_net * (tax_rate / 100)
            
            subtotal += line_net
            tax_amount += line_tax
        
        discount_amount = float(data.get('discount_amount', 0))
        total_amount = subtotal + tax_amount - discount_amount
        
        # إنشاء رقم فاتورة تلقائي
        invoice_number = data.get('invoice_number')
        if not invoice_number:
            last_invoice = db.execute(
                'SELECT invoice_number FROM invoices ORDER BY id DESC LIMIT 1'
            ).fetchone()
            
            if last_invoice:
                last_num = int(last_invoice['invoice_number'].split('-')[-1])
                invoice_number = f"INV-{last_num + 1:06d}"
            else:
                invoice_number = "INV-000001"
        
        # إدراج الفاتورة
        invoice_date = data.get('invoice_date', datetime.now().date().isoformat())
        due_date = data.get('due_date', (datetime.now() + timedelta(days=30)).date().isoformat())
        
        cursor = db.execute('''
            INSERT INTO invoices 
            (invoice_number, customer_id, company_id, invoice_date, due_date,
             subtotal, tax_amount, discount_amount, total_amount, status, notes)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            invoice_number, data['customer_id'], 1,  # company_id افتراضي
            invoice_date, due_date, subtotal, tax_amount, discount_amount,
            total_amount, 'draft', data.get('notes', '')
        ))
        
        invoice_id = cursor.lastrowid
        
        # إدراج عناصر الفاتورة
        for item in data['items']:
            quantity = float(item.get('quantity', 1))
            unit_price = float(item.get('unit_price', 0))
            tax_rate = float(item.get('tax_rate', 15.0))
            discount_percentage = float(item.get('discount_percentage', 0))
            
            line_subtotal = quantity * unit_price
            line_discount = line_subtotal * (discount_percentage / 100)
            line_total = line_subtotal - line_discount + (line_subtotal - line_discount) * (tax_rate / 100)
            
            db.execute('''
                INSERT INTO invoice_items 
                (invoice_id, product_id, description, quantity, unit_price,
                 discount_percentage, tax_rate, line_total)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                invoice_id, item.get('product_id'), item.get('description', ''),
                quantity, unit_price, discount_percentage, tax_rate, line_total
            ))
        
        # إنشاء QR Code متوافق مع ZATCA
        qr_data = {
            'seller_name': app.config['COMPANY_NAME'],
            'tax_number': app.config['COMPANY_TAX_ID'],
            'timestamp': datetime.now().isoformat(),
            'total_amount': total_amount,
            'tax_amount': tax_amount,
            'invoice_number': invoice_number
        }
        
        qr_code = ZATCAService.generate_tlv_qr(qr_data)
        qr_image = ZATCAService.generate_qr_image(qr_code)
        
        # تحديث الفاتورة بـ QR Code
        db.execute(
            'UPDATE invoices SET qr_code = ? WHERE id = ?',
            (qr_code, invoice_id)
        )
        
        db.commit()
        
        return jsonify({
            'id': invoice_id,
            'invoice_number': invoice_number,
            'total_amount': total_amount,
            'tax_amount': tax_amount,
            'qr_code': qr_code,
            'qr_image': qr_image,
            'message': 'تم إنشاء الفاتورة بنجاح'
        }), 201
        
    except Exception as e:
        logger.error(f"خطأ في إنشاء الفاتورة: {e}")
        return jsonify({'message': 'خطأ في إنشاء الفاتورة'}), 500

@app.route('/api/analytics/sales', methods=['GET'])
def sales_analytics():
    """تحليل المبيعات"""
    try:
        db = get_db()
        
        # فترة التحليل (آخر 30 يوم)
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=30)
        
        # إحصائيات المبيعات
        sales_summary = db.execute('''
            SELECT 
                COUNT(*) as invoice_count,
                COALESCE(SUM(total_amount), 0) as total_sales,
                COALESCE(SUM(tax_amount), 0) as total_tax,
                COALESCE(AVG(total_amount), 0) as average_invoice
            FROM invoices 
            WHERE invoice_date BETWEEN ? AND ?
            AND status != 'cancelled'
        ''', (start_date, end_date)).fetchone()
        
        # أفضل المنتجات مبيعاً
        top_products = db.execute('''
            SELECT 
                p.name,
                SUM(ii.quantity) as total_quantity,
                SUM(ii.line_total) as total_sales
            FROM invoice_items ii
            JOIN invoices i ON ii.invoice_id = i.id
            LEFT JOIN products p ON ii.product_id = p.id
            WHERE i.invoice_date BETWEEN ? AND ?
            AND i.status != 'cancelled'
            GROUP BY COALESCE(p.name, ii.description)
            ORDER BY total_sales DESC
            LIMIT 5
        ''', (start_date, end_date)).fetchall()
        
        return jsonify({
            'period': {
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat()
            },
            'summary': {
                'invoice_count': sales_summary['invoice_count'],
                'total_sales': float(sales_summary['total_sales']),
                'total_tax': float(sales_summary['total_tax']),
                'average_invoice': float(sales_summary['average_invoice'])
            },
            'top_products': [
                {
                    'name': row['name'] or 'منتج غير محدد',
                    'total_quantity': float(row['total_quantity']),
                    'total_sales': float(row['total_sales'])
                }
                for row in top_products
            ]
        })
        
    except Exception as e:
        logger.error(f"خطأ في تحليل المبيعات: {e}")
        return jsonify({'message': 'خطأ في تحليل المبيعات'}), 500

@app.route('/api/seed-data', methods=['POST'])
def seed_data():
    """إضافة بيانات تجريبية"""
    try:
        db = get_db()
        
        # إضافة شركة تجريبية
        db.execute('''
            INSERT OR IGNORE INTO companies (name, tax_id, address, phone, email)
            VALUES (?, ?, ?, ?, ?)
        ''', (
            'شركة التقنية المتقدمة',
            '***************',
            'الرياض، المملكة العربية السعودية',
            '+966112345678',
            '<EMAIL>'
        ))
        
        # إضافة عملاء تجريبيين
        customers_data = [
            ('عميل تجريبي 1', '310123456789001', 'الرياض', '+966501234567', '<EMAIL>', 'business'),
            ('عميل تجريبي 2', '310123456789002', 'جدة', '+966502345678', '<EMAIL>', 'individual'),
            ('شركة الابتكار التقني', '310123456789003', 'الدمام', '+966503456789', '<EMAIL>', 'business')
        ]
        
        for customer in customers_data:
            db.execute('''
                INSERT OR IGNORE INTO customers (name, tax_id, address, phone, email, customer_type, company_id)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', customer + (1,))
        
        # إضافة منتجات تجريبية
        products_data = [
            ('خدمة استشارية', 'استشارات تقنية متخصصة', 'CONS-001', 1000.00, 500.00, 15.0, 'خدمات', 'ساعة', 0, 0, 1),
            ('برنامج محاسبة', 'برنامج محاسبة متكامل', 'SOFT-001', 5000.00, 2000.00, 15.0, 'برمجيات', 'رخصة', 10, 2, 0),
            ('دعم فني', 'دعم فني شهري', 'SUPP-001', 500.00, 200.00, 15.0, 'خدمات', 'شهر', 0, 0, 1),
            ('تدريب تقني', 'دورة تدريبية متخصصة', 'TRAIN-001', 2000.00, 800.00, 15.0, 'تدريب', 'دورة', 5, 1, 0)
        ]
        
        for product in products_data:
            db.execute('''
                INSERT OR IGNORE INTO products 
                (name, description, sku, price, cost, tax_rate, category, unit, 
                 stock_quantity, min_stock_level, is_service, company_id)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', product + (1,))
        
        db.commit()
        
        return jsonify({'message': 'تم إضافة البيانات التجريبية بنجاح'})
        
    except Exception as e:
        logger.error(f"خطأ في إضافة البيانات التجريبية: {e}")
        return jsonify({'message': 'خطأ في إضافة البيانات التجريبية'}), 500

# إعداد التطبيق
with app.app_context():
    init_db()

@app.teardown_appcontext
def close_db_connection(exception):
    """إغلاق اتصال قاعدة البيانات"""
    close_db(exception)

@app.errorhandler(404)
def not_found(error):
    return jsonify({'message': 'المسار غير موجود'}), 404

@app.errorhandler(500)
def internal_error(error):
    logger.error(f"خطأ داخلي: {error}")
    return jsonify({'message': 'خطأ داخلي في الخادم'}), 500

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    return jsonify({
        'message': 'نظام المحاسبة الإلكتروني المتوافق مع ZATCA - نسخة الإنتاج',
        'version': '2.0.0',
        'status': 'running',
        'features': [
            'إدارة الشركات والعملاء المحسنة',
            'إدارة المنتجات مع المخزون',
            'فواتير متوافقة مع ZATCA v2.0',
            'تحليل المبيعات المتقدم',
            'نظام أمان محسن',
            'تسجيل النشاطات',
            'نسخ احتياطي تلقائي'
        ]
    })

if __name__ == '__main__':
    # تهيئة قاعدة البيانات
    try:
        init_db()
        logger.info("تم تهيئة قاعدة البيانات بنجاح")
    except Exception as e:
        logger.error(f"خطأ في التهيئة: {e}")
    
    # تشغيل التطبيق
    port = int(os.environ.get('PORT', 5003))
    debug = os.environ.get('FLASK_ENV') == 'development'
    
    logger.info(f"بدء تشغيل نظام المحاسبة المحسن على المنفذ {port}")
    app.run(host='0.0.0.0', port=port, debug=debug)

