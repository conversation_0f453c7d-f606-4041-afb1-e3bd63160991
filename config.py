import os
from datetime import timedelta
from dotenv import load_dotenv

# تحميل المتغيرات من ملف .env
basedir = os.path.abspath(os.path.dirname(__file__))
load_dotenv(os.path.join(basedir, '.env'))

class Config:
    """إعدادات التطبيق الرئيسية"""
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'default-fallback-secret-key-for-dev'
    
    # إعدادات قاعدة البيانات
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'sqlite:///' + os.path.join(basedir, 'database', 'app.db')
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # إعدادات JWT
    JWT_EXPIRATION_DELTA = timedelta(hours=24)