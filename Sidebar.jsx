import React from 'react';
import { Link } from 'react-router-dom';
import { 
  HomeIcon, 
  BuildingOfficeIcon, 
  UserGroupIcon, 
  CubeTransparentIcon, 
  DocumentTextIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline';

const Sidebar = () => {
  const navigation = [
    { name: 'لوحة التحكم', href: '/dashboard', icon: HomeIcon },
    { name: 'الشركات', href: '/companies', icon: BuildingOfficeIcon },
    { name: 'العملاء', href: '/customers', icon: UserGroupIcon },
    { name: 'المنتجات والخدمات', href: '/products', icon: CubeTransparentIcon },
    { name: 'الفواتير', href: '/invoices', icon: DocumentTextIcon },
    { name: 'الإعدادات', href: '/settings', icon: Cog6ToothIcon },
  ];

  return (
    <div className="flex flex-col h-full bg-gray-800 text-white w-64 space-y-6 py-7 px-2">
      <div className="flex items-center justify-center px-4">
        <h2 className="text-2xl font-bold text-white">المحاسب الذكي</h2>
      </div>
      <nav className="flex-1">
        {navigation.map((item) => (
          <Link
            key={item.name}
            to={item.href}
            className="flex items-center px-4 py-2 mt-2 text-gray-300 hover:bg-gray-700 hover:text-white rounded-md"
          >
            <item.icon className="h-6 w-6 ml-3" />
            <span className="text-sm font-medium">{item.name}</span>
          </Link>
        ))}
      </nav>
    </div>
  );
};

export default Sidebar;

