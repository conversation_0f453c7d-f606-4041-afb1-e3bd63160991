import os
import sys

# DON'T CHANGE THIS !!!
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from flask import Flask, jsonify
from flask_cors import CORS
from config import Config
from src.models import db

def create_app(config_class=Config):
    app = Flask(__name__)
    app.config.from_object(config_class)

    # Initialize extensions
    CORS(app, resources={r"/api/*": {"origins": "*"}})
    db.init_app(app)

    # Import and register blueprints
    from src.routes.user import user_bp
    from src.routes.company import company_bp
    from src.routes.customer import customer_bp
    from src.routes.product import product_bp
    from src.routes.invoice import invoice_bp
    from src.routes.zatca import zatca_bp

    app.register_blueprint(user_bp, url_prefix='/api/users')
    app.register_blueprint(company_bp, url_prefix='/api/companies')
    app.register_blueprint(customer_bp, url_prefix='/api/customers')
    app.register_blueprint(product_bp, url_prefix='/api/products')
    app.register_blueprint(invoice_bp, url_prefix='/api/invoices')
    app.register_blueprint(zatca_bp, url_prefix='/api/zatca')

    @app.route('/api/health')
    def health_check():
        return jsonify({'status': 'healthy', 'message': 'نظام المحاسبة يعمل بشكل جيد'})

    return app

# Create app instance
app = create_app()
 
if __name__ == '__main__':
    with app.app_context():
        db.create_all() # Create tables if they don't exist
    app.run(host='0.0.0.0', port=5002, debug=True)
