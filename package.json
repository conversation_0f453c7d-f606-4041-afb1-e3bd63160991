{"name": "accounting-desktop", "productName": "نظام المحاسبة الإلكتروني", "version": "1.0.0", "description": "نظام محاسبة احترافي متوافق مع متطلبات هيئة الزكاة والدخل السعودية", "main": "main.js", "homepage": "./", "author": {"name": "فريق التطوير", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"start": "electron .", "dev": "NODE_ENV=development electron .", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "pack": "electron-builder --dir", "dist": "npm run build"}, "build": {"appId": "com.accounting.desktop", "productName": "نظام المحاسبة الإلكتروني", "directories": {"output": "dist-electron"}, "files": ["main.js", "preload.js", "dist/**/*", "assets/**/*", "node_modules/**/*"], "extraResources": [{"from": "../accounting_frontend/dist", "to": "dist", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "icon": "assets/icon.ico"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "icon": "assets/icon.icns", "category": "public.app-category.business"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}], "icon": "assets/icon.png", "category": "Office"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "نظام المحاسبة الإلكتروني"}}, "devDependencies": {"electron": "^37.2.0", "electron-builder": "^26.0.12"}, "keywords": ["accounting", "invoice", "saudi", "zatca", "desktop", "electron"]}