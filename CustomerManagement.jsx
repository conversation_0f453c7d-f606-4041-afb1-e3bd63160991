import React, { useState, useEffect } from 'react';
import { customersAPI } from '../services/api';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter } from '@/components/ui/dialog';
import { PencilIcon, TrashIcon, PlusIcon } from '@heroicons/react/24/outline';
import { useAuth } from '../contexts/AuthContext';

const CustomerManagement = () => {
  const { user } = useAuth();
  const [customers, setCustomers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentCustomer, setCurrentCustomer] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    tax_id: '',
    address: '',
    email: '',
    phone: ''
  });

  useEffect(() => {
    if (user && user.company_id) {
      fetchCustomers(user.company_id);
    }
  }, [user]);

  const fetchCustomers = async (companyId) => {
    try {
      setLoading(true);
      const response = await customersAPI.getAll(companyId);
      setCustomers(response.data);
    } catch (err) {
      setError('فشل في جلب بيانات العملاء.');
      console.error('Error fetching customers:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { id, value } = e.target;
    setFormData(prev => ({ ...prev, [id]: value }));
  };

  const handleAddEditCustomer = async (e) => {
    e.preventDefault();
    setError('');
    try {
      if (currentCustomer) {
        await customersAPI.update(currentCustomer.customer_id, formData);
      } else {
        await customersAPI.create({ ...formData, company_id: user.company_id });
      }
      fetchCustomers(user.company_id);
      setIsModalOpen(false);
      setFormData({
        name: '',
        tax_id: '',
        address: '',
        email: '',
        phone: ''
      });
      setCurrentCustomer(null);
    } catch (err) {
      setError(err.response?.data?.error || 'فشل في حفظ بيانات العميل.');
      console.error('Error saving customer:', err);
    }
  };

  const handleDeleteCustomer = async (customerId) => {
    if (window.confirm('هل أنت متأكد أنك تريد حذف هذا العميل؟')) {
      setError('');
      try {
        await customersAPI.delete(customerId);
        fetchCustomers(user.company_id);
      } catch (err) {
        setError(err.response?.data?.error || 'فشل في حذف العميل.');
        console.error('Error deleting customer:', err);
      }
    }
  };

  const openEditModal = (customer) => {
    setCurrentCustomer(customer);
    setFormData({
      name: customer.name,
      tax_id: customer.tax_id,
      address: customer.address,
      email: customer.email,
      phone: customer.phone
    });
    setIsModalOpen(true);
  };

  const openAddModal = () => {
    setCurrentCustomer(null);
    setFormData({
      name: '',
      tax_id: '',
      address: '',
      email: '',
      phone: ''
    });
    setIsModalOpen(true);
  };

  if (loading) {
    return <div className="text-center py-8">جاري التحميل...</div>;
  }

  if (error) {
    return <div className="text-center py-8 text-red-500">خطأ: {error}</div>;
  }

  return (
    <div className="container mx-auto p-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-2xl font-bold">إدارة العملاء</CardTitle>
          <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
            <DialogTrigger asChild>
              <Button onClick={openAddModal}>
                <PlusIcon className="h-5 w-5 ml-2" /> إضافة عميل جديد
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>{currentCustomer ? 'تعديل عميل' : 'إضافة عميل جديد'}</DialogTitle>
                <DialogDescription>
                  {currentCustomer ? 'قم بتعديل بيانات العميل.' : 'قم بإضافة عميل جديد إلى النظام.'}
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleAddEditCustomer} className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="name" className="text-right">الاسم</Label>
                  <Input id="name" value={formData.name} onChange={handleInputChange} className="col-span-3" required />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="tax_id" className="text-right">الرقم الضريبي</Label>
                  <Input id="tax_id" value={formData.tax_id} onChange={handleInputChange} className="col-span-3" />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="address" className="text-right">العنوان</Label>
                  <Input id="address" value={formData.address} onChange={handleInputChange} className="col-span-3" />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="email" className="text-right">البريد الإلكتروني</Label>
                  <Input id="email" type="email" value={formData.email} onChange={handleInputChange} className="col-span-3" />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="phone" className="text-right">رقم الهاتف</Label>
                  <Input id="phone" value={formData.phone} onChange={handleInputChange} className="col-span-3" />
                </div>
                <DialogFooter>
                  <Button type="submit">{currentCustomer ? 'حفظ التعديلات' : 'إضافة عميل'}</Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>الاسم</TableHead>
                <TableHead>الرقم الضريبي</TableHead>
                <TableHead>البريد الإلكتروني</TableHead>
                <TableHead>الهاتف</TableHead>
                <TableHead className="text-right">الإجراءات</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {customers.map((customer) => (
                <TableRow key={customer.customer_id}>
                  <TableCell className="font-medium">{customer.name}</TableCell>
                  <TableCell>{customer.tax_id}</TableCell>
                  <TableCell>{customer.email}</TableCell>
                  <TableCell>{customer.phone}</TableCell>
                  <TableCell className="text-right">
                    <Button variant="ghost" size="sm" onClick={() => openEditModal(customer)} className="ml-2">
                      <PencilIcon className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => handleDeleteCustomer(customer.customer_id)}>
                      <TrashIcon className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default CustomerManagement;

