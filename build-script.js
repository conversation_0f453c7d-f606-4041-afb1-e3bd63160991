const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 بدء عملية بناء تطبيق سطح المكتب...');

// التحقق من وجود مجلد الواجهة الأمامية
const frontendPath = path.join(__dirname, '..', 'accounting_frontend');
const frontendDistPath = path.join(frontendPath, 'dist');

if (!fs.existsSync(frontendPath)) {
  console.error('❌ مجلد الواجهة الأمامية غير موجود!');
  process.exit(1);
}

console.log('📦 بناء الواجهة الأمامية...');
try {
  // الانتقال إلى مجلد الواجهة الأمامية وبناؤها
  process.chdir(frontendPath);
  execSync('npm run build', { stdio: 'inherit' });
  console.log('✅ تم بناء الواجهة الأمامية بنجاح');
} catch (error) {
  console.error('❌ فشل في بناء الواجهة الأمامية:', error.message);
  process.exit(1);
}

// العودة إلى مجلد سطح المكتب
process.chdir(__dirname);

// نسخ ملفات الواجهة الأمامية
console.log('📁 نسخ ملفات الواجهة الأمامية...');
const desktopDistPath = path.join(__dirname, 'dist');

try {
  // حذف المجلد القديم إن وجد
  if (fs.existsSync(desktopDistPath)) {
    fs.rmSync(desktopDistPath, { recursive: true, force: true });
  }
  
  // نسخ الملفات
  execSync(`cp -r "${frontendDistPath}" "${desktopDistPath}"`, { stdio: 'inherit' });
  console.log('✅ تم نسخ ملفات الواجهة الأمامية بنجاح');
} catch (error) {
  console.error('❌ فشل في نسخ ملفات الواجهة الأمامية:', error.message);
  process.exit(1);
}

// تعديل ملف index.html لتطبيق سطح المكتب
console.log('🔧 تعديل ملف index.html...');
try {
  const indexPath = path.join(desktopDistPath, 'index.html');
  let indexContent = fs.readFileSync(indexPath, 'utf8');
  
  // إضافة تعديلات خاصة بتطبيق سطح المكتب
  indexContent = indexContent.replace(
    '<head>',
    `<head>
    <meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; connect-src 'self' http://localhost:* ws://localhost:*;">
    <style>
      .electron-app {
        user-select: none;
        -webkit-user-select: none;
      }
      .electron-app input, .electron-app textarea {
        user-select: text;
        -webkit-user-select: text;
      }
    </style>`
  );
  
  fs.writeFileSync(indexPath, indexContent);
  console.log('✅ تم تعديل ملف index.html بنجاح');
} catch (error) {
  console.error('❌ فشل في تعديل ملف index.html:', error.message);
  process.exit(1);
}

console.log('🎉 تم الانتهاء من إعداد ملفات التطبيق بنجاح!');
console.log('💡 يمكنك الآن تشغيل التطبيق باستخدام: npm start');
console.log('📦 أو بناء التطبيق للتوزيع باستخدام: npm run build');

