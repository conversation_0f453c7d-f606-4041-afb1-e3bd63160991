const { contextBridge, ipcRenderer } = require('electron');

// تعريض APIs آمنة للواجهة الأمامية
contextBridge.exposeInMainWorld('electronAPI', {
  // معلومات التطبيق
  getAppVersion: () => ipcRenderer.invoke('get-app-version'),
  
  // حوارات النظام
  showMessageBox: (options) => ipcRenderer.invoke('show-message-box', options),
  showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
  showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options),
  
  // الاستماع لأحداث القائمة
  onMenuAction: (callback) => {
    ipcRenderer.on('menu-action', (event, action) => callback(action));
  },
  
  // إزالة المستمعين
  removeAllListeners: (channel) => {
    ipcRenderer.removeAllListeners(channel);
  },
  
  // معلومات النظام
  platform: process.platform,
  
  // طباعة
  print: () => {
    window.print();
  }
});

// إضافة معلومات إضافية للنافذة
window.addEventListener('DOMContentLoaded', () => {
  // إضافة كلاس للتمييز بين الويب وسطح المكتب
  document.body.classList.add('electron-app');
  
  // إضافة معلومات النظام
  document.documentElement.setAttribute('data-platform', process.platform);
});

